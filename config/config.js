const { url } = require('inspector');

require('dotenv').config();


const neonDBConnectionString = process.env.NEON_DB_CONNECTION_STRING || "";
// const neonDBStaging = process.env.NEON_DB_STAGING || "";
// const neonDBProductionStaging = process.env.NEON_DB_STAGING_MAIN || "";



const development = {
    dialect: 'postgres',
    username: process.env.DEV_DB_USERNAME || "",
    password: process.env.DEV_DB_PASSWORD || "",
    database: process.env.DEV_DB_NAME || "",
    host: process.env.DEV_DB_HOST || "",
};


const testing = {
    // url: neonDBStaging,
    dialectOptions: {
        ssl: {
            require: true,
        }
    },
    dialect: 'postgres',
}

const production = {
    url: neonDBConnectionString,
    dialectOptions: {
        ssl: {
            require: true,
        }
    },
    dialect: 'postgres',
}






module.exports = {
    development: {
        // ...development,
        ...production,
    },
    testing: {
        // ...testing,
        ...production,
    },
    production: {
        ...production,
    },
};