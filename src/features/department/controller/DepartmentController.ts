import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get, pick } from "lodash";
import { DEPARTMENT_STATUS } from "../models/DepartmentMisc";
import { ICreateDepartment } from "../models/IDepartment";

export class DepartmentController {

    static async create(req: Request, res: Response, next: NextFunction) {

        const userId = get(req, "user_id",);

        const payload = pick(req.body, ["name", "status",]) as ICreateDepartment;
        payload.status = DEPARTMENT_STATUS.ACTIVE;
        payload.createdById = BigInt(userId!);
        payload.name = payload.name.toLowerCase();

        const result = await RepoProvider.departmentRepo.create(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async update(req: Request, res: Response, next: NextFunction) {

        const id = BigInt(get(req.params, "id"));

        const userId = get(req, "user_id",);


        const payload = {
            ...req.body,
            updatedById: Number(userId),
        };

        const result = await RepoProvider.departmentRepo.update(id, payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async delete(req: Request, res: Response, next: NextFunction) {
        const ids: any = pick(req.body, "ids");

        const userId = get(req, "user_id",);


        const result = await RepoProvider.departmentRepo.delete(ids, Number(userId));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {

        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));

        const result = await RepoProvider.departmentRepo.getAll(page, pageSize);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.departmentRepo.getById(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async searchByText(req: Request, res: Response, next: NextFunction) {
        const text = get(req.query, "text") as string;
        const result = await RepoProvider.departmentRepo.searchByText(text);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }
}
