import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { ICreateDepartment, IDepartment } from '../models/IDepartment';
import { DEPARTMENT_STATUS } from '../models/DepartmentMisc';
import { RepoProvider } from '../../../core/RepoProvider';
import { CoreUserTable } from '../../users/core/database/CoreUserTable';


class DepartmentTable extends Model<IDepartment, ICreateDepartment> {
    declare createdBy: CoreUserTable;
}

DepartmentTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            // get() {
            //     const value = this.dataValues.id;
            //     if (value) {
            //         return BigInt(value);
            //     }
            // },
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true,
        },

        status: {
            type: DataTypes.ENUM(...Object.values(DEPARTMENT_STATUS)),
            allowNull: false,
        },
        isDefault: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
        },

        createdById: {
            type: DataTypes.BIGINT,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.BIGINT,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.BIGINT,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        underscored: true,
        indexes: [
            {
                unique: true,
                fields: ["name"],
                name: 'unique_department_name',
            },
        ],
        tableName: 'departments',
        timestamps: true,
        paranoid: true,
    },
);


DepartmentTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "Department",
        instance,
        options
    );
});

DepartmentTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "Department",
        instance,
        options
    );
});

DepartmentTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "Department",
        instance,
        options
    );
});


export { DepartmentTable };
