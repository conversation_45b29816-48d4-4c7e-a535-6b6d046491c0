import { Model, ModelStatic } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { ILogRepo } from "./ILogsRepo";
import { HelperMethods } from "../../../core/HelperMethods";
import { LogsTable } from "../database/LogsTable";

export class PostgresLogsRepo implements ILogRepo {

    logModelAction = async (
        action: string,
        modelName: string,
        instance: any,
        options: any,
    ) => {
        try {
            const primaryKey = (instance.constructor as ModelStatic<Model<any>>)
                .primaryKeyAttributes[0];

            let dataToLog: Record<string, any> = {};

            if (action === "create") {
                dataToLog = instance.toJSON();
            } else if (action === "update") {
                const changes: Record<string, any> = {};

                for (const key in instance.dataValues) {
                    const currentValue = instance[key];
                    const previousValue = instance.previous(key);

                    if (currentValue !== previousValue) {
                        changes[key] = {
                            before: previousValue,
                            after: currentValue,
                        };
                    }
                }

                dataToLog = changes;
            } else if (action === "delete") {
                dataToLog = instance.toJSON();
            }
            await LogsTable.create({
                action,
                model: modelName,
                recordId: instance.get(primaryKey),
                data: dataToLog,
                userId: options.userId,
                createdAt: new Date(),
            });
        } catch (error) {
            console.error("Error logging action:", error);
        }
    };

    // async getLogsByModelNameAndId(modelName: string, recordId: number): Promise<APIBaseResponse<PaginatedBaseResponse<LogsTable> | null>> {
    //     try {
    //         const logs = await LogsTable.findAll({
    //             where: {
    //                 model: modelName,
    //                 recordId: recordId,
    //             },
    //             order: [['createdAt', 'DESC']],
    //         });

    //         return logs.map((log) => log.toJSON());
    //     } catch (error) {
    //         HelperMethods.handleError(error);
    //         return HelperMethods.getErrorResponse();
    //     }
    // }

    async getAllLogs(page: number, pageSize: number): Promise<APIBaseResponse<PaginatedBaseResponse<LogsTable> | null>> {
        try {

            const offset = (page - 1) * pageSize;

            const { count, rows } = await LogsTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
            });

            const totalPages = Math.ceil(count / pageSize);

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: rows,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }


    resetPasswordLog = async (
        action: string,
        modelName: string,
        instance: any,
        userId: bigint,
    ):Promise<APIBaseResponse<null>>  => {
        try {
            let dataToLog: Record<string, any> = {};
             dataToLog["data"] = instance;

            await LogsTable.create({
                action,
                model: modelName,
                recordId: Number(userId),
                data: dataToLog,
                userId: Number(userId),
                createdAt: new Date(),
            });

            return HelperMethods.getSuccessResponse(null);

        } catch (error) {
            console.error("Error logging action:", error);
            return HelperMethods.getErrorResponse("Error while saving logs!");
        }
    };

}