import { InterfaceMetaData } from "../../../core/CoreInterfaces";
import { ISingleRawMaterialDetails } from "../../raw_material/models/IRawMaterial";
import { ISupplier } from "../../supplier/models/ISupplier";
import { PurchaseOrderTable } from "../database/PurchaseOrderTable";


interface IPurchaseOrder extends InterfaceMetaData {
    poNumber: string;
    supplierId: bigint;
    expectedDate: Date;
    receivedDate: Date | null;
    fromDepartmentId: bigint;
    toDepartmentId: bigint;
    supplierContactPerson?: string;
}

interface IPurchaseOrderItems {
    rawMaterialId: bigint;
    qty: number;
}


interface IPurchaseOrderPayload extends IPurchaseOrder {
    items: IPurchaseOrderItems[];
}

interface IUpdatePurchaseOrderPayload extends IPurchaseOrderPayload {
    id: bigint;
}

interface IPurchaseOrderResponse {
    id: number;
    poNumber: string;
    supplier: ISupplier;
    expectedDate: Date;
    items: IPurchaseOrderResponseItems[];
    createdByName: string;
    createdAt: Date;
    fromDepartment: {
        id: number;
        name: string;
    };
    toDepartment: {
        id: number;
        name: string;
    };
    supplierContactPerson?: string;
}


interface IPurchaseOrderResponseItems {
    item: ISingleRawMaterialDetails,
    qty: number;
}

const purchaseOrderParser = (data: PurchaseOrderTable): IPurchaseOrderResponse => {
    return {
        id: Number(data.dataValues.id),
        poNumber: data.dataValues.poNumber,
        createdByName: data.createdBy.dataValues.firstName+' '+data.createdBy.dataValues.lastName,
        createdAt: data.dataValues.createdAt,
        supplier: {
            id: BigInt(data.dataValues.supplierId),
            name: data.supplier.dataValues.name,
            email: data.supplier.dataValues.email,
            phone: data.supplier.dataValues.phone,
            gst: data.supplier.dataValues.gst,
            pan: data.supplier.dataValues.pan,

            addressId: data.supplier.dataValues.addressId,
            status: data.supplier.dataValues.status,
            createdAt: data.supplier.dataValues.createdAt,
            updatedAt: data.supplier.dataValues.updatedAt,
            createdById: data.supplier.dataValues.createdById,
            updatedById: data.supplier.dataValues.updatedById,
            deletedAt: data.supplier.dataValues.deletedAt,
            deletedById: data.supplier.dataValues.deletedById
        },
        expectedDate: data.dataValues.expectedDate,
        fromDepartment: {
            id: Number(data.fromDepartment.dataValues.id),
            name: data.fromDepartment.dataValues.name,
        },
        toDepartment: {
            id: Number(data.toDepartment.dataValues.id),
            name: data.toDepartment.dataValues.name,
        },
        supplierContactPerson: data.dataValues.supplierContactPerson,
        items: data.purchaseOrderItems.map((item) => {
            return {
                item: {
                    id: BigInt(item.rawMaterial.dataValues.id),
                    name: item.rawMaterial.dataValues.name,
                    unitName: item.rawMaterial.unit.dataValues.name,
                    categoryName: item.rawMaterial.category.dataValues.name,
                    price: 0,
                    deletedAt: item.rawMaterial.dataValues.deletedAt,
                    deletedById: item.rawMaterial.dataValues.deletedById,
                    status: item.rawMaterial.dataValues.status,
                    sku: item.rawMaterial.dataValues.sku,
                    unitId: item.rawMaterial.dataValues.unitId,
                    categoryId: item.rawMaterial.dataValues.categoryId,
                    createdAt: item.rawMaterial.dataValues.createdAt,
                    updatedAt: item.rawMaterial.dataValues.updatedAt,
                    createdById: item.rawMaterial.dataValues.createdById,
                    updatedById: item.rawMaterial.dataValues.updatedById,
                    gstPercentage: item.rawMaterial.dataValues.gstPercentage,
                    msq: item.rawMaterial.dataValues.msq,
                    hsn: item.rawMaterial.dataValues.hsn,
                },
                qty: Number(item.dataValues.qty),
            }

        })
    }
}

type CreatePurchaseOrder = Pick<IPurchaseOrder, "poNumber" | "supplierId" | "expectedDate" | "receivedDate" | "fromDepartmentId" | "toDepartmentId" | "supplierContactPerson" | "createdById">;

type UpdatePurchaseOrder = Pick<IPurchaseOrder, "poNumber" | "supplierId" | "expectedDate" | "receivedDate" | "fromDepartmentId" | "toDepartmentId" | "supplierContactPerson" | "updatedById">;

export {
    IPurchaseOrder, CreatePurchaseOrder, UpdatePurchaseOrder, IPurchaseOrderPayload, IUpdatePurchaseOrderPayload, IPurchaseOrderResponse, purchaseOrderParser,
    IPurchaseOrderResponseItems,
};