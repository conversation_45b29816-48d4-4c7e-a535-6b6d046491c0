import { InterfaceMetaData } from "../../../core/CoreInterfaces";


interface IPurchaseOrderItem extends InterfaceMetaData {
    purchaseOrderId:bigint;
    rawMaterialId:bigint;
    qty:number;
}

type CreatePurchaseOrderItem = Pick<IPurchaseOrderItem, "purchaseOrderId" | "rawMaterialId" |"qty" | "createdById">;

type UpdatePurchaseOrderItem = Pick<IPurchaseOrderItem, "purchaseOrderId" | "rawMaterialId" |"qty" | "updatedById">;

export { IPurchaseOrderItem, CreatePurchaseOrderItem, UpdatePurchaseOrderItem };