import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { CreatePurchaseOrder, IPurchaseOrder } from '../models/PurchaseOrder';
import { RepoProvider } from '../../../core/RepoProvider';
import { SupplierTable } from '../../supplier/database/SupplierTable';
import { PurchaseOrderController } from '../controller/PurchaseOrderController';
import { PurchaseOrderItemTable } from './PurchaseOrderItemTable';
import { CoreUserTable } from '../../users/core/database/CoreUserTable';
import { DepartmentTable } from '../../department/database/DepartmentTable';


class PurchaseOrderTable extends Model<IPurchaseOrder, CreatePurchaseOrder> {

    declare supplier:SupplierTable;
    declare purchaseOrderItems:PurchaseOrderItemTable[];
    declare createdBy: CoreUserTable;
    declare fromDepartment: DepartmentTable;
    declare toDepartment: DepartmentTable;
 }

PurchaseOrderTable.init(
    {
        id: {
            type: DataTypes.BIGINT,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return BigInt(value.toString());
                }
            }
        },
        poNumber: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        supplierId: {
            type: DataTypes.BIGINT,
            allowNull: false,
            get() {
                const value = this.dataValues.supplierId;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        receivedDate:{
            type: DataTypes.DATE,
            allowNull: true,
        },
        expectedDate:{
            type: DataTypes.DATE,
            allowNull: false,
        },
        fromDepartmentId: {
            field: 'from_department_id',
            type: DataTypes.BIGINT,
            allowNull: false,
            get() {
                const value = this.dataValues.fromDepartmentId;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        toDepartmentId: {
            field: 'to_department_id',
            type: DataTypes.BIGINT,
            allowNull: false,
            get() {
                const value = this.dataValues.toDepartmentId;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        supplierContactPerson: {
            field: 'supplier_contact_person',
            type: DataTypes.STRING,
            allowNull: true,
        },
        createdById: {
            type: DataTypes.BIGINT,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.BIGINT,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.BIGINT,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'purchase_orders',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                unique: true,
                fields: ["poNumber", "supplierId"],
                name: 'unique_po_number_supplier',
            },
        ],
    },
);




PurchaseOrderTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "purchase_orders",
        instance,
        options
    );
});

PurchaseOrderTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "purchase_orders",
        instance,
        options
    );
});

PurchaseOrderTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "purchase_orders",
        instance,
        options
    );
});

export { PurchaseOrderTable };