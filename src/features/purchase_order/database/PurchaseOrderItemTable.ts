import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { RepoProvider } from '../../../core/RepoProvider';
import { CreatePurchaseOrderItem, IPurchaseOrderItem } from '../models/PurchaseOrderItem';
import { RawMaterialTable } from '../../raw_material/database/RawMaterialTable';


class PurchaseOrderItemTable extends Model<IPurchaseOrderItem, CreatePurchaseOrderItem> {
    declare rawMaterial:RawMaterialTable;
 }

PurchaseOrderItemTable.init(
    {
        id: {
            type: DataTypes.BIGINT,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return BigInt(value.toString());
                }
            }
        },
        purchaseOrderId: {
            type: DataTypes.BIGINT,
            allowNull: false,
            get() {
                const value = this.dataValues.purchaseOrderId;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        rawMaterialId: {
            type: DataTypes.BIGINT,
            allowNull: false,
            get() {
                const value = this.dataValues.rawMaterialId;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        qty:{
            type: DataTypes.DECIMAL(10,2),
            allowNull: false,
        },
        createdById: {
            type: DataTypes.BIGINT,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.BIGINT,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.BIGINT,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'purchase_order_items',
        timestamps: true,
        paranoid: true,
    },
);




PurchaseOrderItemTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "purchase_order_items",
        instance,
        options
    );
});

PurchaseOrderItemTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "purchase_order_items",
        instance,
        options
    );
});

PurchaseOrderItemTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "purchase_order_items",
        instance,
        options
    );
});

export { PurchaseOrderItemTable };