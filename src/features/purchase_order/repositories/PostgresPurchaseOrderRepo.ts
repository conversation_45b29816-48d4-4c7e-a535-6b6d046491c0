import {
  APIBaseResponse,
  PaginatedBaseResponse,
} from '../../../core/CoreInterfaces'
import { HelperMethods } from '../../../core/HelperMethods'
import { sequelizeInit } from '../../../sequelize_init'
import { ItemCategoryTable } from '../../item_category/database/ItemCategoryTable'
import { ItemUnitTable } from '../../item_unit/database/ItemUnitTable'
import { PurchaseInvoicePurchaseOrderMapping } from '../../purchase_invoice/database/PurchaseInvoicePurchaseOrderMapping'
import { RawMaterialPriceTable } from '../../raw_material/database/RawMaterialPriceTable'
import { RawMaterialTable } from '../../raw_material/database/RawMaterialTable'
import { SupplierTable } from '../../supplier/database/SupplierTable'
import { CoreUserTable } from '../../users/core/database/CoreUserTable'
import { DepartmentTable } from '../../department/database/DepartmentTable'
import { PurchaseOrderItemTable } from '../database/PurchaseOrderItemTable'
import { PurchaseOrderTable } from '../database/PurchaseOrderTable'
import { CreatePurchaseOrder, IPurchaseOrder, IPurchaseOrderPayload, IPurchaseOrderResponse, IUpdatePurchaseOrderPayload, purchaseOrderParser, UpdatePurchaseOrder } from '../models/PurchaseOrder'
import { CreatePurchaseOrderItem, UpdatePurchaseOrderItem } from '../models/PurchaseOrderItem'

import { IPurchaseOrderRepo } from './IPurchaseOrderRepo'
import { Op, UniqueConstraintError, WhereOptions } from 'sequelize'

export class PostgresPurchaseOrderRepo implements IPurchaseOrderRepo {
  async create(
    payload: IPurchaseOrderPayload
  ): Promise<APIBaseResponse<any | null>> {
    const transaction = await PurchaseOrderTable.sequelize?.transaction();
    try {

      const purchaseOrderObj: CreatePurchaseOrder = {
        poNumber: payload.poNumber,
        supplierId: payload.supplierId,
        receivedDate: null,
        expectedDate: payload.expectedDate,
        fromDepartmentId: payload.fromDepartmentId,
        toDepartmentId: payload.toDepartmentId,
        supplierContactPerson: payload.supplierContactPerson,
        createdById: payload.createdById,
      }
      const purchaseOrder = await PurchaseOrderTable.create(purchaseOrderObj, {
        userId: payload.createdById,
        transaction
      })

      const purchaseOrderItems: CreatePurchaseOrderItem[] = payload.items.map((item) => {
        return {
          purchaseOrderId: purchaseOrder.dataValues.id.valueOf(),
          rawMaterialId: item.rawMaterialId,
          qty: item.qty,
          createdById: payload.createdById,
        }
      });


      const bulkPurchaseOrderItems = await PurchaseOrderItemTable.bulkCreate(purchaseOrderItems, { userId: payload.createdById, transaction })

      if (transaction) await transaction.commit();
      return HelperMethods.getSuccessResponse(
        'Purchase order created successfully'
      );
    } catch (error) {
      if (transaction) await transaction.rollback();
      HelperMethods.handleError(error)
      if (error instanceof UniqueConstraintError) {
        return HelperMethods.getErrorResponse('Purchase order already exists')
      }
      return HelperMethods.getErrorResponse()
    }
  }

  async update(payload: IUpdatePurchaseOrderPayload): Promise<APIBaseResponse<null>> {
    const transaction = await PurchaseOrderTable.sequelize?.transaction();
    try {


      /* check if already used in an purchase invoice */
      const isAlreadyUsed = await PurchaseInvoicePurchaseOrderMapping.findOne({
        where: {
          purchaseOrderId: payload.id
        }
      });
      if (isAlreadyUsed) {
        return HelperMethods.getErrorResponse("This purchase order is used in an purchase invoice, so you can not edit it.");
      }


      // Update the Purchase Order
      const purchaseOrderObj: UpdatePurchaseOrder = {
        poNumber: payload.poNumber,
        supplierId: payload.supplierId,
        receivedDate: payload.receivedDate,
        expectedDate: payload.expectedDate,
        fromDepartmentId: payload.fromDepartmentId,
        toDepartmentId: payload.toDepartmentId,
        supplierContactPerson: payload.supplierContactPerson,
        updatedById: payload.updatedById,
      };

      const [affectedRows] = await PurchaseOrderTable.update(purchaseOrderObj, {
        where: { id: payload.id },
        transaction,
        userId: payload.updatedById!
      });

      if (affectedRows === 0) {
        throw new Error("Purchase Order not found or no changes detected");
      }

      // Fetch existing Purchase Order Items
      const existingItems: PurchaseOrderItemTable[] = await PurchaseOrderItemTable.findAll({
        where: { purchaseOrderId: payload.id },
        transaction,
      });

      const existingItemIds = new Set(existingItems.map(item => item.dataValues.rawMaterialId.toString()));

      const upsertItems: UpdatePurchaseOrderItem[] = payload.items.map(item => ({
        purchaseOrderId: payload.id,
        rawMaterialId: item.rawMaterialId,
        qty: item.qty,
        updatedById: payload.updatedById,
      }));

      for (const item of upsertItems) {
        if (existingItemIds.has(item.rawMaterialId.toString())) {
          await PurchaseOrderItemTable.update(
            { qty: item.qty, updatedById: payload.updatedById },
            {
              where: { purchaseOrderId: payload.id, rawMaterialId: item.rawMaterialId },
              transaction,
              userId: payload.updatedById!
            }
          );
        } else {
          await PurchaseOrderItemTable.create(
            {
              purchaseOrderId: payload.id,
              rawMaterialId: item.rawMaterialId,
              qty: item.qty,
              createdById: payload.updatedById!,
            },
            { transaction, userId: payload.updatedById! }
          );
        }
      }

      const newItemIds = new Set(payload.items.map(item => item.rawMaterialId.toString()));
      const itemsToDelete = [...existingItemIds].filter(id => !newItemIds.has(id));

      if (itemsToDelete.length > 0) {
        await PurchaseOrderItemTable.destroy({
          where: {
            purchaseOrderId: payload.id,
            rawMaterialId: itemsToDelete,
          },
          transaction,
          userId: payload.updatedById!
        });
      }

      await transaction?.commit();
      return HelperMethods.getSuccessResponse(null, "Purchase Order updated successfully");
    } catch (error) {
      await transaction?.rollback();
      HelperMethods.handleError(error);

      if (error instanceof UniqueConstraintError) {
        return HelperMethods.getErrorResponse("Purchase order already exists");
      }

      return HelperMethods.getErrorResponse();
    }
  }

  async getAll(
    page: number,
    pageSize: number,
    text?: string
  ): Promise<
    APIBaseResponse<PaginatedBaseResponse<IPurchaseOrderResponse> | null>
  > {
    try {
      const whereConditions: WhereOptions<IPurchaseOrder> = {}

      if (text) {
        whereConditions.poNumber = {
          [Op.iLike]: text,
        }
      }
      const offset = (page - 1) * pageSize
      const { count, rows } = await PurchaseOrderTable.findAndCountAll({
        include: [
          {
            model: SupplierTable,
            as: "supplier",
          },
          {
            model: CoreUserTable,
            as: "createdBy",
            attributes: ["firstName", 'lastName'],
          },
          {
            model: DepartmentTable,
            as: "fromDepartment",
            attributes: ["id", "name"],
          },
          {
            model: DepartmentTable,
            as: "toDepartment",
            attributes: ["id", "name"],
          },
          {
            model: PurchaseOrderItemTable,
            as: "purchaseOrderItems",
            include: [
              {
                model: RawMaterialTable,
                as: 'rawMaterial',
                required: true,
                include: [
                  {
                    model: ItemUnitTable,
                    as: "unit",
                    attributes: ["name"],
                  },
                  {
                    model: ItemCategoryTable,
                    as: "category",
                    attributes: ["name"],
                  }
                ]
              }
            ]
          }
        ],
        limit: pageSize,
        offset: offset,
        order: [['createdAt', 'DESC']],
        where: whereConditions,
      })

      const totalPages = Math.ceil(count / pageSize)
      const response: IPurchaseOrderResponse[] = rows.map((data) => purchaseOrderParser(data))
      return HelperMethods.getSuccessResponse(
        {
          currentPage: page,
          totalData: count,
          totalPages: totalPages,
          data: response,
        },
        'Purchase order Retrieved Successfully'
      )
    } catch (error) {
      HelperMethods.handleError(error)
      return HelperMethods.getErrorResponse()
    }
  }


  async getById(id: bigint): Promise<APIBaseResponse<IPurchaseOrderResponse | null>> {
    const transaction = await sequelizeInit.transaction();
    try {
      const result = await PurchaseOrderTable.findOne({
        where: { id },
        include: [
          {
            model: PurchaseOrderItemTable,
            as: 'purchaseOrderItems',
            include: [
              {
                model: RawMaterialTable,
                as: 'rawMaterial',
                include: [
                  {
                    model: ItemUnitTable,
                    as: "unit",
                    attributes: ["name"],
                  },
                  {
                    model: ItemCategoryTable,
                    as: "category",
                    attributes: ["name"],
                  }
                ]
              }
            ]
          },
          {
            model: SupplierTable,
            as: "supplier",
          },
          {
            model: CoreUserTable,
            as: "createdBy",
            attributes: ["firstName", 'lastName'],
          },
          {
            model: DepartmentTable,
            as: "fromDepartment",
            attributes: ["id", "name"],
          },
          {
            model: DepartmentTable,
            as: "toDepartment",
            attributes: ["id", "name"],
          }
        ],
        transaction
      });
      if (!result) {
        return HelperMethods.getErrorResponse('Purchase order not found');
      }

      const priceData = await RawMaterialPriceTable.findAll({
        where:
        {
          supplierId: result.dataValues.supplierId,
          rawMaterialId: {
            [Op.in]: result.purchaseOrderItems.map(item => item.dataValues.rawMaterialId)
          }
        },
        transaction
      });

      await transaction.commit();

      const clientResponse: IPurchaseOrderResponse = {
        id: Number(result.dataValues.id),
        poNumber: result.dataValues.poNumber,
        createdByName: result.createdBy.dataValues.firstName + ' ' + result.createdBy.dataValues.lastName,
        createdAt: result.dataValues.createdAt,
        supplier: {
          ...result.supplier.dataValues,
          id: BigInt(result.dataValues.supplierId),
        },
        expectedDate: result.dataValues.expectedDate,
        fromDepartment: {
          id: Number(result.fromDepartment.dataValues.id),
          name: result.fromDepartment.dataValues.name,
        },
        toDepartment: {
          id: Number(result.toDepartment.dataValues.id),
          name: result.toDepartment.dataValues.name,
        },
        supplierContactPerson: result.dataValues.supplierContactPerson,
        items: result.purchaseOrderItems.map((item) => {
          return {
            qty: Number(item.dataValues.qty),
            item: {
              id: BigInt(item.rawMaterial.dataValues.id),
              name: item.rawMaterial.dataValues.name,
              unitName: item.rawMaterial.unit.dataValues.name,
              categoryName: item.rawMaterial.category.dataValues.name,
              price: Number(priceData.find(data => Number(data.dataValues.rawMaterialId) === Number(item.rawMaterial.dataValues.id))?.dataValues.price ?? 0),
              deletedAt: item.rawMaterial.dataValues.deletedAt,
              deletedById: item.rawMaterial.dataValues.deletedById,
              status: item.rawMaterial.dataValues.status,
              sku: item.rawMaterial.dataValues.sku,
              unitId: item.rawMaterial.dataValues.unitId,
              categoryId: item.rawMaterial.dataValues.categoryId,
              createdAt: item.rawMaterial.dataValues.createdAt,
              updatedAt: item.rawMaterial.dataValues.updatedAt,
              createdById: item.rawMaterial.dataValues.createdById,
              updatedById: item.rawMaterial.dataValues.updatedById,
              gstPercentage: item.rawMaterial.dataValues.gstPercentage,
              msq: item.rawMaterial.dataValues.msq,
              hsn: item.rawMaterial.dataValues.hsn,
            },

          }
        })
      };
      return HelperMethods.getSuccessResponse(clientResponse, "Purchase order Retrieved Successfully");
    } catch (error) {
      HelperMethods.handleError(error);
      return HelperMethods.getErrorResponse();
    }
  }

  async delete(ids: bigint[], deletedById: bigint): Promise<APIBaseResponse<null>> {
    const transaction = await PurchaseOrderTable.sequelize?.transaction();
    try {
      await PurchaseOrderTable.destroy({
        where: { id: ids },
        userId: deletedById,
        transaction,
      });

      await PurchaseOrderItemTable.destroy({
        where: { purchaseOrderId: ids },
        transaction,
        userId: deletedById
      });
      if (transaction) await transaction.commit();
      return HelperMethods.getSuccessResponse(null, "Purchase order deleted successfully");
    } catch (error) {
      if (transaction) await transaction.rollback();
      HelperMethods.handleError(error);
      return HelperMethods.getErrorResponse();
    }
  }

  async searchByPoNumber(text: string): Promise<APIBaseResponse<IPurchaseOrderResponse[] | null>> {
    try {

      const result = await PurchaseOrderTable.findAll({
        where: {
          poNumber: {
            [Op.iLike]: text,
          },
        },
        include: [
          {
            model: PurchaseOrderItemTable,
            as: 'purchaseOrderItems',
            include: [
              {
                model: RawMaterialTable,
                as: 'rawMaterial',
                include: [
                  {
                    model: ItemUnitTable,
                    as: "unit",
                    attributes: ["name"],
                  },
                  {
                    model: ItemCategoryTable,
                    as: "category",
                    attributes: ["name"],
                  }
                ]
              },

            ]
          },
          {
            model: SupplierTable,
            as: "supplier",
          },
          {
            model: CoreUserTable,
            as: "createdBy",
            attributes: ["firstName", 'lastName'],
          },
          {
            model: DepartmentTable,
            as: "fromDepartment",
            attributes: ["id", "name"],
          },
          {
            model: DepartmentTable,
            as: "toDepartment",
            attributes: ["id", "name"],
          }
        ],
      });
      if (!result) {
        return HelperMethods.getErrorResponse('Purchase order not found');
      }

      /* get price */
      const priceData = await RawMaterialPriceTable.findAll({
        where:
        {
          supplierId: {
            [Op.in]: result.map(item => item.dataValues.supplierId)
          },
          rawMaterialId: {
            [Op.in]: result.flatMap(item => item.purchaseOrderItems.map(poItem => poItem.dataValues.rawMaterialId))
          }
        },
      });



      const purchaseOrder = result.map((data) => {


        return {
          id: Number(data.dataValues.id),
          poNumber: data.dataValues.poNumber,
          createdByName: data.createdBy.dataValues.firstName + ' ' + data.createdBy.dataValues.lastName,
          createdAt: data.dataValues.createdAt,
          supplier: {
            id: BigInt(data.dataValues.supplierId),
            name: data.supplier.dataValues.name,
            email: data.supplier.dataValues.email,
            phone: data.supplier.dataValues.phone,
            gst: data.supplier.dataValues.gst,
            pan: data.supplier.dataValues.pan,
            addressId: data.supplier.dataValues.addressId,
            status: data.supplier.dataValues.status,
            createdAt: data.supplier.dataValues.createdAt,
            updatedAt: data.supplier.dataValues.updatedAt,
            createdById: data.supplier.dataValues.createdById,
            updatedById: data.supplier.dataValues.updatedById,
            deletedAt: data.supplier.dataValues.deletedAt,
            deletedById: data.supplier.dataValues.deletedById
          },
          expectedDate: data.dataValues.expectedDate,
          fromDepartment: {
            id: Number(data.fromDepartment.dataValues.id),
            name: data.fromDepartment.dataValues.name,
          },
          toDepartment: {
            id: Number(data.toDepartment.dataValues.id),
            name: data.toDepartment.dataValues.name,
          },
          supplierContactPerson: data.dataValues.supplierContactPerson,
          items: data.purchaseOrderItems.map((item) => {
            return {
              item: {
                id: BigInt(item.rawMaterial.dataValues.id),
                name: item.rawMaterial.dataValues.name,
                unitName: item.rawMaterial.unit.dataValues.name,
                categoryName: item.rawMaterial.category.dataValues.name,
                price: Number(priceData.find(data => data.dataValues.rawMaterialId === item.rawMaterial.dataValues.id && data.dataValues.supplierId === data.dataValues.supplierId)?.dataValues.price ?? 0),
                deletedAt: item.rawMaterial.dataValues.deletedAt,
                deletedById: item.rawMaterial.dataValues.deletedById,
                status: item.rawMaterial.dataValues.status,
                sku: item.rawMaterial.dataValues.sku,
                unitId: item.rawMaterial.dataValues.unitId,
                categoryId: item.rawMaterial.dataValues.categoryId,
                createdAt: item.rawMaterial.dataValues.createdAt,
                updatedAt: item.rawMaterial.dataValues.updatedAt,
                createdById: item.rawMaterial.dataValues.createdById,
                updatedById: item.rawMaterial.dataValues.updatedById,
                gstPercentage: item.rawMaterial.dataValues.gstPercentage,
                msq: item.rawMaterial.dataValues.msq,
                hsn: item.rawMaterial.dataValues.hsn,
              },
              qty: Number(item.dataValues.qty),
            }

          })
        };
      });
      return HelperMethods.getSuccessResponse(purchaseOrder, "Purchase order Retrieved Successfully");
    } catch (error) {
      HelperMethods.handleError(error);
      return HelperMethods.getErrorResponse();
    }
  }

}
