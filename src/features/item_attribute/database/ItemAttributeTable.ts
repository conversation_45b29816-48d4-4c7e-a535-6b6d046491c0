import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { CreateItemAttribute, IItemAttribute } from '../models/IItemAttribute';
import { ITEM_ATTRIBUTES_STATUS } from '../models/ItemAttributeMisc';
import { RepoProvider } from '../../../core/RepoProvider';


class ItemAttributeTable extends Model<IItemAttribute, CreateItemAttribute> { }

ItemAttributeTable.init(
    {
        id: {
            type: DataTypes.BIGINT,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return BigInt(value.toString());
                }
            }
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        status: {
            type: DataTypes.ENUM(...Object.values(ITEM_ATTRIBUTES_STATUS)),
            allowNull: false,
        },

        createdById: {
            type: DataTypes.BIGINT,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.BIGINT,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.BIGINT,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'item_attributes',
        timestamps: true,
        paranoid: true,
    },
);




ItemAttributeTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "item_attributes",
        instance,
        options
    );
});

ItemAttributeTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "item_attributes",
        instance,
        options
    );
});

ItemAttributeTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "item_attributes",
        instance,
        options
    );
});

export { ItemAttributeTable };