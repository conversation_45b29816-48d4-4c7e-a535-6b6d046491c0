import * as express from "express";
import { RawMaterialValidations } from "../validations/RawMaterialValidations";
import { RawMaterialController } from "../controller/RawMaterialController";
import { AppPermissions } from "../../users/sub_feaures/user_permissions/AppPermissions";
import { permissionsMiddleware } from "../../../middlewares/permissionsMiddleware";

const apiInitialPath = "/raw-materials";
const rawMaterialRouter = express.Router();

rawMaterialRouter.post(apiInitialPath + "/create",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL.CREATE),
    RawMaterialValidations.validateCreate, RawMaterialController.create);

rawMaterialRouter.put(apiInitialPath + "/update/:id",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL.UPDATE),
    RawMaterialValidations.validateUpdate, RawMaterialController.update);

rawMaterialRouter.delete(apiInitialPath + "/delete", RawMaterialValidations.validateDelete, RawMaterialController.delete);


rawMaterialRouter.get(apiInitialPath + "/search",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL.READ),
    RawMaterialValidations.validateSearch, RawMaterialController.search);

rawMaterialRouter.get(apiInitialPath + "/searchByText",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL.READ),
    RawMaterialValidations.validateSearchByText, RawMaterialController.searchByText);

rawMaterialRouter.get(apiInitialPath + "/",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL.READ),
    RawMaterialValidations.validateGetAll, RawMaterialController.getAll);

rawMaterialRouter.get(apiInitialPath + "/:id",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL.READ),
    RawMaterialValidations.validateGetById, RawMaterialController.getById);

export { rawMaterialRouter };