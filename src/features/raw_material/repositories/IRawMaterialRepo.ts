import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { RawMaterialTable } from "../database/RawMaterialTable";
import { ICreateRawMaterial, IRawMaterialDetails, ISingleRawMaterialDetails } from "../models/IRawMaterial";

export interface IRawMaterialRepo {
    create(payload: ICreateRawMaterial): Promise<APIBaseResponse<RawMaterialTable | null>>;

    update(id: bigint, payload: ICreateRawMaterial): Promise<APIBaseResponse<void>>;

    getAll(page: number, pageSize: number,startDate?:Date,endDate?:Date): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialDetails> | null>>;

    searchByText(text: string): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialDetails> | null>>;

    getById(id: number): Promise<APIBaseResponse<IRawMaterialDetails | null>>;

    delete(ids: number[], deletedById: number): Promise<APIBaseResponse<null>>;

    search(text: string, supplierId?: number): Promise<APIBaseResponse<ISingleRawMaterialDetails[] | null>>;
}