import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { sequelizeInit } from "../../../sequelize_init";
import { ItemCategoryTable } from "../../item_category/database/ItemCategoryTable";
import { ItemUnitTable } from "../../item_unit/database/ItemUnitTable";
import { RawMaterialStockTable } from "../../raw_material_stock/database/RawMaterialStockTable";
import { RawMaterialTable } from "../database/RawMaterialTable";
import { ICreateRawMaterial, ICreateRawMaterialPrice, IRawMaterialDetails, IRawMaterialPriceDetails, ISingleRawMaterialDetails } from "../models/IRawMaterial";
import { RAW_MATERIAL_STAUS } from "../models/RawMaterialMisc";
import { IRawMaterialRepo } from "./IRawMaterialRepo";
import { Op, Sequelize, UniqueConstraintError } from "sequelize";
import { RawMaterialPriceTable } from "../database/RawMaterialPriceTable";
import { SupplierTable } from "../../supplier/database/SupplierTable";
import { ICreateRawMaterialStock } from "../../raw_material_stock/models/IRawMaterialStock";
import { PaginationProvider } from "../../pagination/PaginationProvider";

export class PostgresRawMaterialRepo implements IRawMaterialRepo {
    delete(ids: number[], deletedById: number): Promise<APIBaseResponse<null>> {
        throw new Error("Method not implemented.");
    }

    async create(payload: ICreateRawMaterial): Promise<APIBaseResponse<RawMaterialTable | null>> {
        const transaction = await sequelizeInit.transaction();
        try {


            const savedRawMaterial = await RawMaterialTable.create(payload, {
                transaction: transaction,
                userId: payload.createdById,
            });

            const stockPayload: ICreateRawMaterialStock = {
                rawMaterialId: savedRawMaterial.getDataValue('id'),
                totalStock: 0,
                assignedStock: 0,
                usableStock: 0,
                createdById: payload.createdById,
            };

            /* create raw material stock */
            await RawMaterialStockTable.create(stockPayload, {
                transaction: transaction,
                userId: payload.createdById,
            });
            /* create raw material price */

            const pricePayload: ICreateRawMaterialPrice[] = [];


            for (const data of payload.priceData) {
                pricePayload.push({
                    price: data.price,
                    moq: data.moq,
                    supplierId: data.supplierId,
                    rawMaterialId: savedRawMaterial.getDataValue('id'),
                    createdById: payload.createdById,
                });
            }


            await RawMaterialPriceTable.bulkCreate(pricePayload, {
                transaction: transaction,
                userId: payload.createdById,
                individualHooks: true,

            });


            await transaction.commit();

            return HelperMethods.getSuccessResponse(savedRawMaterial);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                if (error.errors[0].path === 'name') {
                    return HelperMethods.getErrorResponse('Name already exists');
                }
                else if (error.errors[0].path === 'sku') {
                    return HelperMethods.getErrorResponse('SKU already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async update(id: bigint, payload: ICreateRawMaterial): Promise<APIBaseResponse<null>> {
        const transaction = await sequelizeInit.transaction();
        try {

            const deletionUpdates = {
                deletedAt: new Date(),
                deletedById: payload.updatedById,
            };
            if (payload.status === RAW_MATERIAL_STAUS.DELETED) {
                Object.assign(payload, deletionUpdates);
            }

            await RawMaterialTable.update(payload, {
                where: {
                    id: id
                },
                transaction: transaction,
                userId: payload.updatedById!,
                individualHooks: true,

            });

            /* delete old data */
            await RawMaterialPriceTable.destroy({
                where: {
                    rawMaterialId: id
                },
                transaction: transaction,
                userId: payload.updatedById!,
            });

            /* create raw material price */

            const pricePayload: ICreateRawMaterialPrice[] = [];


            for (const data of payload.priceData) {
                pricePayload.push({
                    price: data.price,
                    moq: data.moq,
                    supplierId: data.supplierId,
                    rawMaterialId: id,
                    createdById: payload.updatedById!,
                });
            }


            await RawMaterialPriceTable.bulkCreate(pricePayload, {
                transaction: transaction,
                individualHooks: true,
                userId: payload.updatedById!,

            });


            await transaction.commit();

            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                if (error.errors[0].path === 'name') {
                    return HelperMethods.getErrorResponse('Name already exists');
                }
                else if (error.errors[0].path === 'sku') {
                    return HelperMethods.getErrorResponse('SKU already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async getAll(page: number, pageSize: number, startDate?: Date, endDate?: Date): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialDetails> | null>> {
        try {

            const includeData = [
                {
                    model: ItemUnitTable,
                    attributes: ['name'],
                    as: "unit",
                    where: {
                        deletedAt: null
                    }
                },
                {
                    model: ItemCategoryTable,
                    attributes: ['name'],
                    as: "category",
                    where: {
                        deletedAt: null
                    }
                },
                {
                    model: RawMaterialPriceTable,
                    as: "prices",
                    include: [
                        {
                            model: SupplierTable,
                            attributes: ['name'],
                            as: "supplier",
                        }
                    ]
                }
            ]

            let where = {
                status: RAW_MATERIAL_STAUS.ACTIVE,
                deletedAt: null,
            }

            const paginatedData = await new PaginationProvider<any, RawMaterialTable>().getPaginatedRecords(RawMaterialTable, { include: includeData, where, page: page, limit: pageSize, dateColumn: "createdAt", startDate: startDate ?? undefined, endDate: endDate ?? undefined })

            const data: IRawMaterialDetails[] = [];
            const priceData: IRawMaterialPriceDetails[] = [];


            for (const item of paginatedData.rows) {


                for (const data of item.prices) {
                    priceData.push({
                        price: data.getDataValue('price'),
                        moq: data.getDataValue('moq'),
                        supplierId: data.getDataValue('supplierId'),
                        supplier: data.supplier.getDataValue('name'),
                        rawMaterialId: item.getDataValue('id'),
                        rawMaterial: item.getDataValue('name'),
                    });
                }

                data.push({
                    id: item.getDataValue('id'),
                    name: item.getDataValue('name'),
                    unitId: item.getDataValue('unitId'),
                    categoryId: item.getDataValue('categoryId'),
                    sku: item.getDataValue('sku'),
                    msq: item.getDataValue('msq'),
                    status: item.getDataValue('status'),
                    unitName: item.unit.getDataValue('name'),
                    categoryName: item.category.getDataValue('name'),
                    hsn: item.getDataValue('hsn'),
                    gstPercentage: item.getDataValue('gstPercentage'),

                    priceData: priceData,

                    createdAt: item.getDataValue('createdAt'),
                    updatedAt: item.getDataValue('updatedAt'),
                    createdById: item.getDataValue('createdById'),
                    updatedById: item.getDataValue('updatedById'),
                    deletedById: item.getDataValue('deletedById'),
                    deletedAt: item.getDataValue('deletedAt'),
                });
            }

            return HelperMethods.getSuccessResponse({
                currentPage: paginatedData.currentPage,
                totalData: paginatedData.total,
                totalPages: paginatedData.totalPages,
                data: data,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async searchByText(text: string): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialDetails> | null>> {
        try {
            const { count, rows } = await RawMaterialTable.findAndCountAll({
                limit: 10,
                order: [['createdAt', 'DESC']],
                where: {


                    [Op.or]: [
                        {
                            name: {
                                [Op.iLike]: `%${text}%`
                            }
                        },
                        {
                            sku: {
                                [Op.iLike]: `%${text}%`
                            }
                        }
                    ],

                    status: RAW_MATERIAL_STAUS.ACTIVE
                },
                include: [
                    {
                        model: ItemUnitTable,
                        attributes: ['name'],
                        as: "unit",
                        where: {
                            deletedAt: null
                        }
                    },
                    {
                        model: ItemCategoryTable,
                        attributes: ['name'],
                        as: "category",
                        where: {
                            deletedAt: null
                        }
                    },
                    {
                        model: RawMaterialPriceTable,
                        as: "prices",
                        include: [
                            {
                                model: SupplierTable,
                                attributes: ['name'],
                                as: "supplier",
                                where: {
                                    deletedAt: null
                                }
                            }
                        ]
                    }
                ]
            });

            const totalPages = 1;

            const data: IRawMaterialDetails[] = [];
            const priceData: IRawMaterialPriceDetails[] = [];


            for (const item of rows) {
                for (const data of item.prices) {
                    priceData.push({
                        price: data.getDataValue('price'),
                        moq: data.getDataValue('moq'),
                        supplierId: data.getDataValue('supplierId'),
                        supplier: data.supplier.getDataValue('name'),
                        rawMaterialId: item.getDataValue('id'),
                        rawMaterial: item.getDataValue('name'),
                    });
                }


                data.push({
                    id: item.getDataValue('id'),
                    name: item.getDataValue('name'),
                    unitId: item.getDataValue('unitId'),
                    categoryId: item.getDataValue('categoryId'),
                    sku: item.getDataValue('sku'),
                    msq: item.getDataValue('msq'),
                    status: item.getDataValue('status'),
                    unitName: item.unit.getDataValue('name'),
                    categoryName: item.category.getDataValue('name'),
                    hsn: item.getDataValue('hsn'),
                    gstPercentage: item.getDataValue('gstPercentage'),

                    priceData: priceData,

                    createdAt: item.getDataValue('createdAt'),
                    updatedAt: item.getDataValue('updatedAt'),
                    createdById: item.getDataValue('createdById'),
                    updatedById: item.getDataValue('updatedById'),
                    deletedById: item.getDataValue('deletedById'),
                    deletedAt: item.getDataValue('deletedAt'),
                });
            }

            return HelperMethods.getSuccessResponse({
                currentPage: 1,
                totalData: count,
                totalPages: totalPages,
                data: data,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getById(id: number): Promise<APIBaseResponse<IRawMaterialDetails | null>> {
        try {
            const result = await RawMaterialTable.findByPk(id, {
                include: [
                    {
                        model: RawMaterialPriceTable,
                        as: "prices",
                        include: [
                            {
                                model: SupplierTable,
                                attributes: ['name'],
                                as: "supplier"
                            }
                        ]
                    },
                    {
                        model: ItemUnitTable,
                        attributes: ['name'],
                        as: "unit",
                    },
                    {
                        model: ItemCategoryTable,
                        attributes: ['name'],
                        as: "category",
                    }
                ]
            });
            if (!result) {
                return HelperMethods.getErrorResponse("No data found");
            }


            const priceData: IRawMaterialPriceDetails[] = [];
            for (const item of result.prices) {
                priceData.push({
                    price: Number(item.getDataValue('price')),
                    moq: Number(item.getDataValue('moq')),
                    supplierId: BigInt(item.getDataValue('supplierId')),
                    supplier: item.supplier.getDataValue('name'),
                    rawMaterialId: BigInt(item.getDataValue('rawMaterialId')),
                    rawMaterial: result.getDataValue('name'),
                });
            }
            const data: IRawMaterialDetails = {
                id: BigInt(result.getDataValue('id')),
                name: result.getDataValue('name'),
                unitId: BigInt(result.getDataValue('unitId')),
                categoryId: BigInt(result.getDataValue('categoryId')),
                sku: result.getDataValue('sku'),
                msq: Number(result.getDataValue('msq')),
                status: result.getDataValue('status'),
                unitName: result.unit.getDataValue('name'),
                categoryName: result.category.getDataValue('name'),
                priceData: priceData,
                hsn: result.getDataValue('hsn'),
                gstPercentage: Number(result.getDataValue('gstPercentage')),
                createdAt: result.getDataValue('createdAt'),
                updatedAt: result.getDataValue('updatedAt'),
                createdById: result.getDataValue('createdById'),
                updatedById: result.getDataValue('updatedById'),
                deletedById: result.getDataValue('deletedById'),
                deletedAt: result.getDataValue('deletedAt'),
            };

            return HelperMethods.getSuccessResponse(data);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }




    async search(text: string, supplierId?: number): Promise<APIBaseResponse<ISingleRawMaterialDetails[] | null>> {
        try {

            /* get Raw materials */
            const result = await RawMaterialTable.findAll({
                where: {
                    [Op.or]: [
                        { name: { [Op.iLike]: text } },
                        { name: { [Op.iLike]: `%${text}%` } }
                    ]
                },
                include: {
                    model: ItemUnitTable,
                    as: "unit",
                    attributes: ["name"],
                },
                order: [
                    Sequelize.literal(`CASE WHEN "RawMaterialTable"."name" ILIKE '${text}' THEN 1 ELSE 2 END`),
                    ['name', 'ASC']
                ],
                limit: 5,

            });




            let filteredData: ISingleRawMaterialDetails[] = [];


            for (const item of result) {

                filteredData.push(
                    {
                        categoryId: item.dataValues.categoryId,
                        categoryName: "",
                        hsn: item.dataValues.hsn,
                        id: BigInt(item.dataValues.id),
                        name: item.dataValues.name,
                        price: 0,
                        deletedAt: item.dataValues.deletedAt,
                        deletedById: item.dataValues.deletedById,
                        status: item.dataValues.status,
                        sku: item.dataValues.sku,
                        unitId: item.dataValues.unitId,
                        unitName: item.unit.dataValues.name,
                        updatedAt: item.dataValues.updatedAt,
                        updatedById: item.dataValues.updatedById,
                        createdAt: item.dataValues.createdAt,
                        createdById: item.dataValues.createdById,
                        gstPercentage: item.dataValues.gstPercentage,
                        msq: item.dataValues.msq,
                    }
                );
            }



            if (supplierId) {
                filteredData = [];

                /* check for suppliers */
                const linkedData = await RawMaterialPriceTable.findAll({
                    where: {
                        supplierId: supplierId,
                        rawMaterialId: {
                            [Op.in]: result.map(item => item.getDataValue('id'))
                        }
                    },
                    limit: 5,
                });


                for (const item of result) {

                    const foundData = linkedData.find(data => data.getDataValue('rawMaterialId') === item.getDataValue('id'));
                    if (!foundData) {
                        continue;
                    }


                    filteredData.push(

                        {
                            categoryId: item.dataValues.categoryId,
                            categoryName: "",
                            hsn: item.dataValues.hsn,
                            id: BigInt(item.dataValues.id),
                            name: item.dataValues.name,
                            price: Number(foundData.getDataValue('price')),
                            deletedAt: item.dataValues.deletedAt,
                            deletedById: item.dataValues.deletedById,
                            status: item.dataValues.status,
                            sku: item.dataValues.sku,
                            unitId: item.dataValues.unitId,
                            unitName: item.unit.dataValues.name,
                            updatedAt: item.dataValues.updatedAt,
                            updatedById: item.dataValues.updatedById,
                            createdAt: item.dataValues.createdAt,
                            createdById: item.dataValues.createdById,
                            gstPercentage: item.dataValues.gstPercentage,
                            msq: item.dataValues.msq,

                        }
                    );
                }


            }


            return HelperMethods.getSuccessResponse(filteredData);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
}