import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get, pick } from "lodash";
import { RAW_MATERIAL_STAUS } from "../models/RawMaterialMisc";
import { ICreateRawMaterial } from "../models/IRawMaterial";

export class RawMaterialController {

    static async create(req: Request, res: Response, next: NextFunction) {

        const userId = get(req, "user_id",);

        const payload = pick(req.body, ["name", "unitId", "status", "sku", "msq", "priceData", "categoryId", "hsn", "gstPercentage"]) as ICreateRawMaterial;
        payload.status = RAW_MATERIAL_STAUS.ACTIVE;
        payload.createdById = BigInt(userId!);
        payload.name = payload.name.toLowerCase();
        payload.sku = payload.sku.toLowerCase();


        const result = await RepoProvider.rawMaterialRepo.create(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async update(req: Request, res: Response, next: NextFunction) {

        const id = BigInt(get(req.params, "id"));
        const userId = get(req, "user_id",);

        const payload = {
            ...req.body,
            updatedById: Number(userId),
        };

        const result = await RepoProvider.rawMaterialRepo.update(id, payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async delete(req: Request, res: Response, next: NextFunction) {
        const ids: any = pick(req.body, "ids");

        const userId = get(req, "user_id",);


        const result = await RepoProvider.rawMaterialRepo.delete(ids, Number(userId));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {

        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));

        const startDate = (get(req.query, "startDate")==="undefined"?undefined:get(req.query,"startDate")??undefined) as Date | undefined;
        const endDate = (get(req.query, "endDate")==="undefined"?undefined:get(req.query,"endDate")??undefined) as Date | undefined;


        const result = await RepoProvider.rawMaterialRepo.getAll(page, pageSize,startDate,endDate);

        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.rawMaterialRepo.getById(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }


    static async search(req: Request, res: Response, next: NextFunction) {
        let text = get(req.query, "text") as string;
        text = text.replace(/'/g, "''")
        const supplierId = get(req.query, "supplierId");
        const result = await RepoProvider.rawMaterialRepo.search(text, supplierId ?
            Number(supplierId)
            : undefined);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }


    static async searchByText(req: Request, res: Response, next: NextFunction) {
        const text = get(req.query, "text") as string;
        const result = await RepoProvider.rawMaterialRepo.searchByText(text);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }
}