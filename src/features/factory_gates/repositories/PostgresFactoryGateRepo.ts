import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { sequelizeInit } from "../../../sequelize_init";
import { FactoryGateTable } from "../database/FactoryGateTable";
import { ICreateFactoryGate } from "../models/IFactoryGate";
import { FACTORY_GATE_STAUS } from "../models/FactoryGateMisc";
import { IFactoryGateRepo } from "./IFactoryGateRepo";
import { Op, UniqueConstraintError } from "sequelize";

export class PostgresFactoryGateRepo implements IFactoryGateRepo {

    async create(vendor: ICreateFactoryGate): Promise<APIBaseResponse<FactoryGateTable | null>> {
        try {
            const result = await FactoryGateTable.create(vendor, {
                userId: vendor.createdById,
            });
            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse('Name already exists');
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async update(id: number, factoryGate: ICreateFactoryGate): Promise<APIBaseResponse<null>> {
        try {
            const deletionUpdates = {
                deletedAt: new Date(),
                deletedById: factoryGate.updatedById,
            };
            if (factoryGate.status === FACTORY_GATE_STAUS.DELETED) {
                Object.assign(factoryGate, deletionUpdates);
            }
            await FactoryGateTable.update(factoryGate, {
                where: {
                    id: id
                },
                userId: factoryGate.createdById,
                individualHooks: true,

            });
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse('Name already exists');

            }
            return HelperMethods.getErrorResponse();
        }
    }

    async getAll(page: number, pageSize: number): Promise<APIBaseResponse<PaginatedBaseResponse<FactoryGateTable> | null>> {
        try {
            const offset = (page - 1) * pageSize;
            const { count, rows } = await FactoryGateTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                where: {
                    status: FACTORY_GATE_STAUS.ACTIVE,
                    deletedAt: null,
                },
            });

            const totalPages = Math.ceil(count / pageSize);

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: rows,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async searchByText(text: string): Promise<APIBaseResponse<PaginatedBaseResponse<FactoryGateTable> | null>> {
        try {
            const { count, rows } = await FactoryGateTable.findAndCountAll({
                limit: 10,
                order: [['createdAt', 'DESC']],
                where: {

                    name: {
                        [Op.iLike]: `%${text}%`
                    },


                    status: FACTORY_GATE_STAUS.ACTIVE
                },
            });

            const totalPages = 1;

            return HelperMethods.getSuccessResponse({
                currentPage: 1,
                totalData: count,
                totalPages: totalPages,
                data: rows,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getById(id: number): Promise<APIBaseResponse<FactoryGateTable | null>> {
        try {
            const result = await FactoryGateTable.findByPk(id);
            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async delete(ids: bigint[], deletedById: bigint): Promise<APIBaseResponse<null>> {
        try {
            const transaction = await sequelizeInit.transaction();
            await FactoryGateTable.update({
                status: FACTORY_GATE_STAUS.DELETED,
                deletedById: deletedById,
            }, {
                where: {
                    id: {
                        [Op.in]: ids
                    }
                },
                transaction: transaction,
                userId: deletedById,
                individualHooks: true,

            });
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
}