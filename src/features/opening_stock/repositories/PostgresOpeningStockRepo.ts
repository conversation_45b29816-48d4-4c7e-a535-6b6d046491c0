import { WhereOptions } from 'sequelize'
import {
  APIBaseResponse,
  PaginatedBaseResponse,
} from '../../../core/CoreInterfaces'
import { HelperMethods } from '../../../core/HelperMethods'
import { sequelizeInit } from '../../../sequelize_init'
import { OpeningStockTable } from '../database/OpeningStockTable'
import { IOpeningStockRequest, IOpeningStockResponse, IUpdateOpeningStockRequest } from '../models/IOpeningStock'
import { IOpeningStockRepo } from './IOpeningStockRepo'
import { Op } from 'sequelize'
import { RawMaterialTable } from '../../raw_material/database/RawMaterialTable'
import { RawMaterialStockTable } from '../../raw_material_stock/database/RawMaterialStockTable'
import { IRawMaterial } from '../../raw_material/models/IRawMaterial'
import { CoreUserTable } from '../../users/core/database/CoreUserTable'
import { PaginationProvider } from "../../pagination/PaginationProvider";
import { RawMaterialStockInTable } from '../../raw_material_stock/database/RawMaterialStockInTable'

export class PostgresOpeningStockRepo implements IOpeningStockRepo {
  async create(
    payload: IOpeningStockRequest
  ): Promise<APIBaseResponse<null>> {

    const transaction = await sequelizeInit.transaction();

    try {


      /* first check, there must be no row of that raw material with storage location assigned */
      const existingStock = await RawMaterialStockInTable.findAll({
        where: {
          rawMaterialId: {
            [Op.in]: payload.data.map((data) => data.rawMaterialId),
          },
          storageLocationId: null,
        },
        transaction,
      });

      if (existingStock.length > 0) {
        return HelperMethods.getErrorResponse("Please assign storage location to the existing stock before adjusting stock");
      }

      const openingStock = payload.data.map(data => {
        return {
          rawMaterialId: data.rawMaterialId,
          quantity: data.quantity,
          date: payload.date,
          createdById: payload.createdById,
        }
      })
      /* opening stock */
      const result = await OpeningStockTable.bulkCreate(openingStock, {
        transaction: transaction,
        userId: payload.createdById,
        individualHooks: true,
      })

      /* update raw material stock */
      const promises = result.map(async (item) =>
        RawMaterialStockTable.update({
          totalStock: item.dataValues.quantity,
          usableStock: item.dataValues.quantity,
        }, {
          where: {
            rawMaterialId: item.dataValues.rawMaterialId,
          },
          transaction: transaction,
          userId: payload.createdById,
          individualHooks: true,
        })
      );

      await Promise.all(promises);

      await transaction.commit();

      return HelperMethods.getSuccessResponse(null)
    } catch (error) {
      transaction.rollback();
      HelperMethods.handleError(error)
      return HelperMethods.getErrorResponse()
    }
  }

  async update(payload: IUpdateOpeningStockRequest
  ): Promise<APIBaseResponse<null>> {
    const transaction = await sequelizeInit.transaction();
    try {
      // const existingStock = await OpeningStockTable.findAll({
      //   where: {
      //     id: payload.data.map((data) => data.id),
      //   },
      //   transaction
      // })

      // const revertPromises = existingStock.map(async (item) => {
      //   return RawMaterialStockTable.decrement(['usableStock', 'totalStock'], {
      //     by: item.dataValues.quantity,
      //     where: {
      //       rawMaterialId: item.dataValues.rawMaterialId,
      //     },
      //     transaction,
      //   })
      // })

      // await Promise.all(revertPromises);

      // const updatePromises = payload.data.map(async (data) => {
      //   return OpeningStockTable.update({
      //     rawMaterialId: data.rawMaterialId,
      //     quantity: data.quantity,
      //     updatedAt: new Date(),
      //     updatedById: payload.updatedById,
      //   }, {
      //     where: {
      //       id: data.id,
      //     },
      //     transaction,
      //     individualHooks: true,
      //     userId: payload.updatedById
      //   })
      // })

      // await Promise.all(updatePromises);

      // const applyPromises = payload.data.map(async (data) => {
      //   return RawMaterialStockTable.increment(['usableStock', 'totalStock'], {
      //     by: data.quantity,
      //     where: {
      //       rawMaterialId: data.rawMaterialId,
      //     },
      //     transaction,
      //   });
      // });

      // await Promise.all(applyPromises);

      await transaction.commit();

      return HelperMethods.getSuccessResponse(null);
    } catch (error) {
      await transaction.rollback();
      HelperMethods.handleError(error);
      return HelperMethods.getErrorResponse();
    }
  }


  async getAll(
    page: number,
    pageSize: number,
    text?: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<APIBaseResponse<PaginatedBaseResponse<IOpeningStockResponse> | null>> {
    try {
      const whereConditions: WhereOptions<IRawMaterial> = {
      };

      if (text) {
        whereConditions.name = {
          [Op.iLike]: `%${text}%`
        };
      }

      const includeData = [
        {
          model: RawMaterialTable,
          as: 'rawMaterial',
          where: whereConditions,
          required: true,
        },
        {
          model: CoreUserTable,
          as: 'createdBy',
          required: true,
        }
      ];

      const paginatedData = await new PaginationProvider<any, OpeningStockTable>().getPaginatedRecords(OpeningStockTable, { include: includeData, page: page, limit: pageSize, dateColumn: "createdAt", startDate: startDate ?? undefined, endDate: endDate ?? undefined, order: [['date', 'DESC']] })


      const data: IOpeningStockResponse[] = paginatedData.rows.map(row => {
        return {
          id: BigInt(row.dataValues.id),
          rawMaterial: {
            id: BigInt(row.rawMaterial.dataValues.id),
            name: row.rawMaterial.dataValues.name,
          },
          date: row.dataValues.date,
          quantity: row.dataValues.quantity,
          createdBy: row.createdBy.dataValues.firstName,
          createdAt: row.dataValues.createdAt,
        }
      });

      return HelperMethods.getSuccessResponse({
        currentPage: paginatedData.currentPage,
        totalData: paginatedData.total,
        totalPages: paginatedData.totalPages,
        data: data,
      });
    } catch (error) {
      HelperMethods.handleError(error)
      return HelperMethods.getErrorResponse()
    }
  }

  async getById(id: number): Promise<APIBaseResponse<IOpeningStockResponse | null>> {
    try {
      const includeData = [
        {
          model: RawMaterialTable,
          as: 'rawMaterial',
          required: true,
        },
        {
          model: CoreUserTable,
          as: 'createdBy',
          required: true,
        }
      ];

      const record = await OpeningStockTable.findOne({
        where: { id },
        include: includeData,
      });

      if (!record) {
        return HelperMethods.getErrorResponse("Record not found");
      }

      const data: IOpeningStockResponse = {
        id: BigInt(record.dataValues.id),
        rawMaterial: {
          id: BigInt(record.rawMaterial.dataValues.id),
          name: record.rawMaterial.dataValues.name,
        },
        date: record.dataValues.date,
        quantity: record.dataValues.quantity,
        createdBy: record.createdBy.dataValues.firstName,
        createdAt: record.dataValues.createdAt,
      };

      return HelperMethods.getSuccessResponse(data);
    } catch (error) {
      HelperMethods.handleError(error);
      return HelperMethods.getErrorResponse();
    }
  }
}

