import { NextFunction, Request, Response } from "express";
import { get } from "lodash";
import { ICreateNormalUser, IUpdateNormalUser, ResetPasswordPayload } from "../models/INormalUser";
import { USER_STAUS } from "../../../core/models/UserMisc";
import { RepoProvider } from "../../../../../core/RepoProvider";

export class NormalUserController {

    static async create(req: Request, res: Response, next: NextFunction) {

        const userId = get(req, "user_id");

        const requestBody = req.body as ICreateNormalUser;

        const payload: ICreateNormalUser = {
            firstName: requestBody.firstName.toLowerCase(),
            lastName: requestBody.lastName.toLowerCase(),
            email: requestBody.email.toLowerCase(),
            password: req.body.password,
            mobile: requestBody.mobile,
            roleId: requestBody.roleId,
            firebaseUID: "",
            status: USER_STAUS.ACTIVE,
            createdById: BigInt(userId!),
            address: requestBody.address,
        };

        const result = await RepoProvider.normalUserRepo.create(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async update(req: Request, res: Response, next: NextFunction) {


        const userId = get(req, "user_id",);
        const id = BigInt(get(req.params, "id"));
        const requestBody = req.body as IUpdateNormalUser;

        const payload: IUpdateNormalUser = {
            id: id,
            firstName: requestBody.firstName,
            lastName: requestBody.lastName,
            mobile: requestBody.mobile,
            updatedById: BigInt(userId!),
            roleId: requestBody.roleId,
            status: requestBody.status,
            email: requestBody.email,
            address: requestBody.address,
        };

        const result = await RepoProvider.normalUserRepo.update(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {

        const page = Number(get(req.query, "page"));
        const pageSize = Number(get(req.query, "pageSize"));
        const text = get(req.query, "text") as string | undefined;

        const result = await RepoProvider.normalUserRepo.getAll(page, pageSize, text);

        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const id = get(req.params, "id");
        const result = await RepoProvider.normalUserRepo.getById(Number(id));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async getByFirebaseToken(req: Request, res: Response, next: NextFunction) {
        const userId = get(req, "user_id",);
        const result = await RepoProvider.normalUserRepo.getById(Number(userId));
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async resetPassword(req: Request, res: Response, next: NextFunction) {
        const currentUserId = BigInt(get(req, "user_id",)!);

        const requestBody = req.body as ResetPasswordPayload;

        const payload: ResetPasswordPayload = {
            userId: requestBody.userId,
            password: requestBody.password,
            updatedById: currentUserId,
        };

        const result = await RepoProvider.normalUserRepo.resetPassword(payload);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }

    static async logout(req: Request, res: Response, next: NextFunction) {
        try {
            const userId = get(req, "user_id");
            const firebaseUid = get(req, "firebase_uid");
            
            console.log("🚪 Logout Debug Info:");
            console.log("  - User ID:", userId);
            console.log("  - Firebase UID:", firebaseUid);
            console.log("  - Auth Header:", req.headers.authorization ? "Present" : "Missing");
            
            if (!userId) {
                res.status(400).send({
                    success: false,
                    message: "User ID not found in request. Make sure you're authenticated.",
                    data: false
                });
                return;
            }
            
            // Clear the last activity time from Redis
            const redisKey = `last_activity_time:${userId}`;
            await RepoProvider.redisServerRepository.delete(redisKey);
            console.log(`🚪 User ${userId} logged out successfully - session cleared from Redis`);

            res.status(200).send({
                success: true,
                message: "Logged out successfully",
                data: true
            });

        } catch (error) {
            console.error("❌ Logout error:", error);
            res.status(500).send({
                success: false,
                message: "Failed to logout. Please try again.",
                data: false
            });
        }
    }


}