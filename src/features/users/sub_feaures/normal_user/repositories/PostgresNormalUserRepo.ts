import { Op, UniqueConstraintError, WhereOptions } from "sequelize";
import * as admin from "firebase-admin";
import { INormalUserRepo } from "./INormalUserRepo";
import { ICreateNormalUser, INormalUserResponse, IUpdateNormalUser, ResetPasswordPayload } from "../models/INormalUser";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../../../core/CoreInterfaces";
import { NormalUserTable } from "../database/NormalUserTable";
import { UserRoleTable } from "../../user_roles/database/UserRoleTable";
import { USER_ROLE_STATUS } from "../../user_roles/models/UserRolesMisc";
import { HelperMethods } from "../../../../../core/HelperMethods";
import { RepoProvider } from "../../../../../core/RepoProvider";
import { sequelizeInit } from "../../../../../sequelize_init";
import { CoreUserTable } from "../../../core/database/CoreUserTable";
import { AddressTable } from "../../../../address/database/AddressTable";
import { ADDRESS_STATUS } from "../../../../address/models/AddressMisc";
import { ICoreUser } from "../../../core/models/ICoreUser";
import { USER_STAUS } from "../../../core/models/UserMisc";
import { LogsTable } from "../../../../logs/database/LogsTable";
import { get } from "lodash";


export class PostgresNormalUserRepo implements INormalUserRepo {


    async create(payload: ICreateNormalUser): Promise<APIBaseResponse<NormalUserTable | null>> {

        let firebaseUserUid: string | null = null;
        const transaction = await sequelizeInit.transaction();
        try {

            /* check for valid role */
            const role = await UserRoleTable.findOne({
                where: {
                    id: payload.roleId,
                    status: USER_ROLE_STATUS.ACTIVE
                },

            });

            if (!role) {
                return HelperMethods.getErrorResponse("Invalid role");
            }


            /* create firebase user */
            const firebaseUser = await admin.auth().createUser({
                email: payload.email,
                password: payload.password,
            });

            firebaseUserUid = firebaseUser.uid;

            payload.firebaseUID = firebaseUserUid;

            /* create core user */
            const coreUser = await CoreUserTable.create(payload, {
                transaction: transaction,
                userId: payload.createdById,
            });

            /* create address */
            const address = await AddressTable.create({
                street: payload.address.street,
                postalCode: payload.address.postalCode,
                city: payload.address.city,
                state: payload.address.state,
                country: payload.address.country,
                status: ADDRESS_STATUS.ACTIVE,
                createdById: payload.createdById,
            }, {
                transaction: transaction,
                userId: payload.createdById,
            });

            /* create normal user */
            const result = await NormalUserTable.create({
                coreUserId: coreUser.dataValues.id,
                addressId: address.dataValues.id,

            }, {
                userId: payload.createdById,
                transaction: transaction,
            });

            await transaction.commit();

            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            transaction.rollback();
            if (firebaseUserUid) {
                await admin.auth().deleteUser(firebaseUserUid);
            }
            HelperMethods.handleError(error);

            if ((error as any)?.errorInfo?.code === "auth/email-already-exists") {
                return HelperMethods.getErrorResponse('Email already exists');
            }
            else if ((error as any)?.errorInfo?.code === "auth/invalid-email") {
                return HelperMethods.getErrorResponse('Invalid email');
            }
            else if (error instanceof UniqueConstraintError) {

                if (error.errors[0].path === 'email') {
                    return HelperMethods.getErrorResponse('Email already exists');
                }
                else if (error.errors[0].path === 'mobile') {
                    return HelperMethods.getErrorResponse('Mobile number already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async update(payload: IUpdateNormalUser): Promise<APIBaseResponse<null>> {
        const transaction = await sequelizeInit.transaction();
        try {


            /* check if user exists */

            const user = await CoreUserTable.findOne({
                where: {
                    id: payload.id
                },
                transaction: transaction,
            });

            if (!user) {
                return HelperMethods.getErrorResponse("User not found");
            }


            /* check for valid role */
            const role = await UserRoleTable.findOne({
                where: {

                    id: payload.roleId,
                    status: USER_ROLE_STATUS.ACTIVE
                },

            });

            if (!role) {
                return HelperMethods.getErrorResponse("Invalid role");
            }

            const deletionUpdates = {
                deletedAt: new Date(),
                deletedById: payload.updatedById,
            };
            if (payload.status === USER_STAUS.DELETED) {
                Object.assign(payload, deletionUpdates);
            }

            /* update core user */
            const coreUser = await CoreUserTable.update(payload, {
                where: {
                    id: user.dataValues.id
                },
                transaction: transaction,
                userId: payload.updatedById,
            });

            /* update address */
            const address = await AddressTable.update({
                street: payload.address.street,
                postalCode: payload.address.postalCode,
                city: payload.address.city,
                state: payload.address.state,
                country: payload.address.country,
                status: ADDRESS_STATUS.ACTIVE,
                updatedById: payload.updatedById,
            }, {
                where: {
                    id: payload.address.id
                },
                transaction: transaction,
                userId: payload.updatedById,
            });

            await transaction.commit();
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                if (error.errors[0].path === 'email') {
                    return HelperMethods.getErrorResponse('Email already exists');
                }
                else if (error.errors[0].path === 'phone') {
                    return HelperMethods.getErrorResponse('Contact number already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async getAll(page: number, pageSize: number, text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<INormalUserResponse> | null>> {
        try {
            let whereConditions: WhereOptions<ICoreUser> = {
                status: USER_STAUS.ACTIVE
            };

            if (text) {
                whereConditions = {
                    [Op.or]:
                        [
                            { firstName: { [Op.iLike]: `%${text}%` } },
                            { lastName: { [Op.iLike]: `%${text}%` } },
                            { email: { [Op.iLike]: `%${text}%` } },
                            { mobile: { [Op.iLike]: `%${text}%` } }
                        ],
                    status: USER_STAUS.ACTIVE,
                };
            }

            const offset = (page - 1) * pageSize;
            const { count, rows } = await NormalUserTable.findAndCountAll({
                attributes: ["id", "coreUserId", "addressId"],
                limit: pageSize,
                offset: offset,
                include: [
                    {
                        model: CoreUserTable,
                        as: 'coreUser',
                        where: whereConditions,
                        required: true,
                        order: [['createdAt', 'DESC']],
                        include: [
                            {
                                model: UserRoleTable,
                                as: 'role',
                                where: {
                                    status: USER_ROLE_STATUS.ACTIVE
                                }
                            }
                        ],
                    },
                    {
                        model: AddressTable,
                        as: 'address',
                        where: {
                            status: ADDRESS_STATUS.ACTIVE
                        }
                    }
                ],
            });

            const data: INormalUserResponse[] = rows.map(row => {
                return {
                    id: BigInt(row.dataValues.id),
                    coreUserId: BigInt(row.dataValues.coreUserId),
                    firstName: row.coreUser.dataValues.firstName,
                    lastName: row.coreUser.dataValues.lastName,
                    email: row.coreUser.dataValues.email,
                    mobile: row.coreUser.dataValues.mobile,
                    role: {
                        id: BigInt(row.coreUser.role.dataValues.id),
                        role: row.coreUser.role.dataValues.role,
                    },
                    status: row.coreUser.dataValues.status,
                    address: row.address.dataValues,
                }
            });


            const totalPages = Math.ceil(count / pageSize);
            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: data,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getById(id: number): Promise<APIBaseResponse<INormalUserResponse | null>> {
        try {
            const result = await NormalUserTable.findOne({
                attributes: ["id", "coreUserId", "addressId"],
                where: {
                    coreUserId: id
                },
                include: [
                    {
                        model: CoreUserTable,
                        as: 'coreUser',
                        where: {
                            id: id
                        },
                        required: true,
                        include: [
                            {
                                model: UserRoleTable,
                                as: 'role',
                                where: {
                                    status: USER_ROLE_STATUS.ACTIVE
                                }
                            }
                        ],
                    },
                    {
                        model: AddressTable,
                        as: 'address',
                        where: {
                            status: ADDRESS_STATUS.ACTIVE
                        }
                    }
                ],
            });

            if (!result) {
                return HelperMethods.getErrorResponse("User not found");
            }

            const data: INormalUserResponse = {
                id: BigInt(result.dataValues.id),
                coreUserId: BigInt(result.dataValues.coreUserId),
                firstName: result.coreUser.dataValues.firstName,
                lastName: result.coreUser.dataValues.lastName,
                email: result.coreUser.dataValues.email,
                mobile: result.coreUser.dataValues.mobile,
                role: {
                    id: BigInt(result.coreUser.role.dataValues.id),
                    role: result.coreUser.role.dataValues.role,
                }, status: result.coreUser.dataValues.status,
                address: result.address.dataValues,
            }
            data.address.id = BigInt(data.address.id);
            return HelperMethods.getSuccessResponse(data);

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }


    async resetPassword(payload: ResetPasswordPayload): Promise<APIBaseResponse<null>> {
        try {

            const response = await RepoProvider.coreUserRepo.getById(payload.userId);
            if (!response.success) {
                return HelperMethods.getErrorResponse("User not found!");
            }

            const firebaseUID = response.data!.dataValues.firebaseUID;
            await admin.auth().updateUser(firebaseUID, { password: payload.password });
            await RepoProvider.logRepo.resetPasswordLog("update", "core_user", { data: "Password changed" }, payload.updatedById);
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

}
