import { InterfaceMetaData } from "../../../core/CoreInterfaces";
import { DebitNoteTable } from "../../debit_note/database/DebitNoteTable";
import { PurchaseOrderTable } from "../../purchase_order/database/PurchaseOrderTable";
import { RawMaterialRejectionTable } from "../../raw_material_stock/database/RawMaterialRejectionTable";
import { IRawMaterialRejection } from "../../raw_material_stock/models/IRawMaterialRejection";
import { ISupplier } from "../../supplier/models/ISupplier";
import { PURCHASE_INVOICE_STATUS } from "./PurchaseInvoiceMisc";

interface ICreatePurchaseInvoice {
    invoiceNumber: string;
    invoiceDate: Date;
    poNumber: string;
    poDate: Date;
    supplierId: bigint;
    status: PURCHASE_INVOICE_STATUS;
    purchasedById: bigint;
    factoryGateId: bigint;
    createdById: bigint;
}

interface IRawMaterialReceivedItem {
    rawMaterialId: bigint;
    storageLocationId: bigint | null;
    totalQty: number;
    price: number;
    rejectedQty: number;
    rejectionReason: string | null;
    rejectedById: bigint | null;
    holdQty: number;
    holdReason: string | null;
    holdById: bigint | null;
    excessQty:number,
    replaceableQty: number;
}

interface IRawMaterialReceivedItemResponse extends IRawMaterialReceivedItem {
    rawMaterial: string;
    unit: string;

}

interface IPurchaseInvoiceRequest extends ICreatePurchaseInvoice {
    factoryGateId: bigint;
    rawMaterials: IRawMaterialReceivedItem[];
    purchaseOrderId?: bigint;

}

interface IPurchaseInvoiceUpdateRequest extends Omit<IPurchaseInvoiceRequest, "createdById"> {
    id: bigint;
    factoryGateId: bigint;
    rawMaterials: IRawMaterialReceivedItem[];
    updatedById: bigint;
}


interface IPurchaseInvoice extends ICreatePurchaseInvoice, InterfaceMetaData {

}

interface IPurchaseInvoiceResponse extends IPurchaseInvoice {
    supplier: string;
    entryId: string;
}


interface IPurchaseInvoiceAddNewItemRequest {
    id: bigint;
    updatedById: bigint;
    rawMaterials: IRawMaterialReceivedItem[];
}

interface IPurchaseInvoiceDetailedResponse extends IPurchaseInvoice {
    supplier: ISupplier;
    rawMaterials: IRawMaterialReceivedItemResponse[];
    debitNotes: DebitNoteTable[];
    rawMaterialRejections: IRawMaterialRejection[];
    purchaseOrder:PurchaseOrderTable | null;
}


interface ICreatePurchaseInvoiceEntryMapping {
    purchaseInvoiceId: bigint;
    entryNumber: string;
}

interface IPurchaseInvoiceEntryMapping extends ICreatePurchaseInvoiceEntryMapping {
    id: number;
}

export { IPurchaseInvoice, IPurchaseInvoiceResponse, ICreatePurchaseInvoice, IPurchaseInvoiceRequest, IPurchaseInvoiceDetailedResponse, IPurchaseInvoiceAddNewItemRequest, IRawMaterialReceivedItem, IRawMaterialReceivedItemResponse, IPurchaseInvoiceEntryMapping, ICreatePurchaseInvoiceEntryMapping, IPurchaseInvoiceUpdateRequest };