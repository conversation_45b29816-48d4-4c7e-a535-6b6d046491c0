import { Request, Response, NextFunction } from "express";
import { RawMaterialStockSchema } from "./RawMaterialStockSchema";
import { HelperMethods } from "../../../core/HelperMethods";
import { get, isArray, pick } from "lodash";
import { CoreSchemas } from "../../../core/CoreSchemas";

export class RawMaterialStockValidations {
    static validateReceiveStock = (req: Request, res: Response, next: NextFunction) => {

        const result = RawMaterialStockSchema.receiveStock.safeParse(req.body);

        let errorMessage = "";
        if (!result.success) {

            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString().toUpperCase() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }
        return next();
    }



    static validateGetAll = (req: Request, res: Response, next: NextFunction) => {

        // const result = CoreSchemas.paginationSchema.safeParse(req.query);
        // if (!result.success) {
        //     res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
        //     return;
        // }

        return next();
    }

    static validateGetById = (req: Request, res: Response, next: NextFunction) => {

        const id = get(req.params, "id");
        if (!id || typeof Number(id) !== "number") {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid id"));
            return;
        }

        return next();
    }

    static validateUpdate = (req: Request, res: Response, next: NextFunction) => {

        const id = get(req.params, "id");
        if (!id || typeof Number(id) !== "number") {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid id"));
            return;
        }

        const result = RawMaterialStockSchema.updateStock.safeParse(req.body);

        let errorMessage = "";
        if (!result.success) {

            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString().toUpperCase() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }
        return next();
    }

    static validateAssignStorageToStock = (req: Request, res: Response, next: NextFunction) => {

        const id = get(req.params, "id");
        if (!id || typeof Number(id) !== "number") {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid id"));
            return;
        }

        const result = RawMaterialStockSchema.assignStorageToStock.safeParse(req.body);

        let errorMessage = "";
        if (!result.success) {

            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString().toUpperCase() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }
        return next();
    }

    static validateGetStockInEntryById = (req: Request, res: Response, next: NextFunction) => {

        const id = get(req.params, "id");
        if (!id || typeof Number(id) !== "number") {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid id"));
            return;
        }
        return next();
    }


    static validateIssueStock = (req: Request, res: Response, next: NextFunction) => {

        const result = RawMaterialStockSchema.issueStock.safeParse(req.body);

        let errorMessage = "";
        if (!result.success) {

            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString().toUpperCase() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }
        return next();
    }

    static validateEditStockIssuance = (req: Request, res: Response, next: NextFunction) => {
        // Validate entryId parameter
        const entryId = get(req.params, "entryId");
        if (!entryId || typeof entryId !== "string" || entryId.trim().length < 3) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid entry ID"));
            return;
        }

        const result = RawMaterialStockSchema.editStockIssuance.safeParse(req.body);

        let errorMessage = "";
        if (!result.success) {

            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString().toUpperCase() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }
        return next();
    }

    static validateGetByDateRange = (req: Request, res: Response, next: NextFunction) => {

        const startDate = get(req.query, "startDate");
        const endDate = get(req.query, "endDate");

        if (!startDate || !endDate) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }

        /* vaidate date type */
        if (typeof startDate !== "string" || typeof endDate !== "string") {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
        }

        /* check if dates strings are in yyyy-mm-dd format */
        if (!(startDate as string).match(/^\d{4}-\d{2}-\d{2}$/) || !(endDate as string).match(/^\d{4}-\d{2}-\d{2}$/)) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid date format, must be yyyy-mm-dd"));
            return;
        }

        const result = CoreSchemas.paginationSchema.safeParse(req.query);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }


        return next();
    }

    static validateSearchByRawMaterial = (req: Request, res: Response, next: NextFunction) => {

        const result = RawMaterialStockSchema.searchByRawMaterial.safeParse(req.query);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }


        return next();
    }


    static validateSearchInStockByText = (req: Request, res: Response, next: NextFunction) => {
        const text = get(req.query, "text");
        if (!text) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }

        return next();
    }

    static validateExportByCategory = (req: Request, res: Response, next: NextFunction) => {
        const result = RawMaterialStockSchema.exportByCategory.safeParse(req.params);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }

        return next();
    }

}