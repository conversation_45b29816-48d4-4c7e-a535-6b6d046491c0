import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { IReceiveRawMaterialStock, IRawMaterialStockDetails, IRawMaterialStockInDetails, IRawMaterialStockUpdateRequest, IAssignStorageToStockRequest, IRawMaterialStockDetailsWithPrice } from "../models/IRawMaterialStock";
import { ICreateRawMaterialStockIn } from "../models/IRawMaterialStockIn";
import { ICreateRawMaterialStockIssuance, IRawMaterialStockIssuanceResponse, IUpdateRawMaterialStockIssuance } from "../models/IRawMaterialStockIssuance";

export interface IRawMaterialStockRepo {

    receiveStock(payload: IReceiveRawMaterialStock): Promise<APIBaseResponse<null>>;

    getAll(page: number, pageSize: number): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockDetails> | null>>;

    getById(id: number): Promise<APIBaseResponse<IRawMaterialStockDetails | null>>;

    getByRawMaterialId(id: number): Promise<APIBaseResponse<IRawMaterialStockDetails | null>>;

    getStockIn(page: number, pageSize: number, text?: string, startDate?: Date, endDate?: Date): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockInDetails> | null>>;

    searchInStockByText(text: string, storageLocationNotAssigned: boolean): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockInDetails> | null>>;

    getStockInWithoutStorage(page: number, pageSize: number): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockInDetails> | null>>;

    getStockInById(id: number): Promise<APIBaseResponse<IRawMaterialStockInDetails | null>>;


    updateStock(request: IRawMaterialStockUpdateRequest): Promise<APIBaseResponse<null>>;

    assignStorageToStock(request: IAssignStorageToStockRequest): Promise<APIBaseResponse<null>>;

    issueStock(request: ICreateRawMaterialStockIssuance[]): Promise<APIBaseResponse<null>>;

    getAllStockIssuance(page: number, pageSize: number): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockIssuanceResponse> | null>>;

    getStockIssuanceById(entryId: string): Promise<APIBaseResponse<IRawMaterialStockIssuanceResponse | null>>;

    editStockIssuance(entryId: string, payload: IUpdateRawMaterialStockIssuance[]): Promise<APIBaseResponse<null>>;

    searchStockIssuanceByText(text: string): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockIssuanceResponse> | null>>;

    getStockInByDateRange(startDate: Date, endDate: Date, page: number, pageSize: number): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockInDetails> | null>>;

    searchByRawMaterial(rawMaterialName: string): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockDetails> | null>>;

    exportByCategory(categoryId: number): Promise<APIBaseResponse<IRawMaterialStockDetails[]| null>>;

    exportByCategoryWithPrice(categoryId: number): Promise<APIBaseResponse<IRawMaterialStockDetailsWithPrice[]| null>>;

    // Debug method for local testing - finds stocks with inconsistent or negative values
    getProblematicStocks(): Promise<APIBaseResponse<IRawMaterialStockDetails[] | null>>;
}