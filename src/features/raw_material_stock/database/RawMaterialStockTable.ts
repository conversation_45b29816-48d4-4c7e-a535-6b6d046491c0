import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { IRawMaterialStock, ICreateRawMaterialStock } from '../models/IRawMaterialStock';
import { RawMaterialTable } from '../../raw_material/database/RawMaterialTable';
import { RepoProvider } from '../../../core/RepoProvider';


class RawMaterialStockTable extends Model<IRawMaterialStock, ICreateRawMaterialStock> {

    declare rawMaterial: RawMaterialTable;

}

RawMaterialStockTable.init(
    {
        id: {
            type: DataTypes.BIGINT,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        rawMaterialId: {
            type: DataTypes.BIGINT,
            allowNull: false,
            get() {
                const value = this.dataValues.rawMaterialId;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        totalStock: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        assignedStock: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: true,
        },

        usableStock: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },

        createdById: {
            type: DataTypes.BIGINT,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.BIGINT,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.BIGINT,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'raw-materials-stock',
        timestamps: true,
        paranoid: true,
    },
);



RawMaterialStockTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "RawMaterialStock",
        instance,
        options
    );
});

RawMaterialStockTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "RawMaterialStock",
        instance,
        options
    );
});

RawMaterialStockTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "RawMaterialStock",
        instance,
        options
    );
});


export { RawMaterialStockTable };