import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { CreateItemAttributeValue, IItemAttributeValue } from '../models/IItemAttributeValue';
import { ITEM_ATTRIBUTES_VALUE_STATUS } from '../models/ItemAttributeValueMisc';
import { RepoProvider } from '../../../core/RepoProvider';


class ItemAttributeValueTable extends Model<IItemAttributeValue, CreateItemAttributeValue> { }

ItemAttributeValueTable.init(
    {
        id: {
            type: DataTypes.BIGINT,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return BigInt(value.toString());
                }
            }
        },
        itemAttributeId: {
            type: DataTypes.BIGINT,
            allowNull: false,
            get() {
                const value = this.dataValues.itemAttributeId;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        title: {
            type: DataTypes.STRING,
            allowNull: false,
        },

        value: {
            type: DataTypes.STRING,
            allowNull: false,
        },

        status: {
            type: DataTypes.ENUM(...Object.values(ITEM_ATTRIBUTES_VALUE_STATUS)),
            allowNull: false,
        },

        createdById: {
            type: DataTypes.BIGINT,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.BIGINT,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.BIGINT,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return BigInt(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'item_attributes_values',
        timestamps: true,
        paranoid: true,
    },
);



ItemAttributeValueTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "item_attributes_values",
        instance,
        options
    );
});

ItemAttributeValueTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "item_attributes_values",
        instance,
        options
    );
});

ItemAttributeValueTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "item_attributes_values",
        instance,
        options
    );
});




export { ItemAttributeValueTable };