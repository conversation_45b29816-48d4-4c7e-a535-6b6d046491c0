import { IFactoryGateRepo } from "../features/factory_gates/repositories/IFactoryGateRepo";
import { PostgresFactoryGateRepo } from "../features/factory_gates/repositories/PostgresFactoryGateRepo";
import { IItemAttributeValueRepo } from "../features/item_attribute_value/repositories/IItemAttributeValueRepo";
import { PostgresItemAttributeValueRepo } from "../features/item_attribute_value/repositories/PostgresItemAttributeValueRepo";
import { IItemAttributeRepo } from "../features/item_attribute/repositories/IItemAttributeRepo";
import { PostgresItemAttributeRepo } from "../features/item_attribute/repositories/PostgresItemAttributeRepo";
import { IItemCategoryRepo } from "../features/item_category/repositories/IItemCategoryRepo";
import { PostgresItemCategoryRepo } from "../features/item_category/repositories/PostgresItemCategoryRepo";
import { IItemUnitRepo } from "../features/item_unit/repositories/IItemUnitRepo";
import { PostgresItemUnitRepo } from "../features/item_unit/repositories/PostgresItemUnitRepo";
import { IRawMaterialRepo } from "../features/raw_material/repositories/IRawMaterialRepo";
import { PostgresRawMaterialRepo } from "../features/raw_material/repositories/PostgresRawMaterialRepo";
import { IStorageLocationRepo } from "../features/storage_locations/repositories/IStorageLocationRepo";
import { PostgresStorageLocationRepo } from "../features/storage_locations/repositories/PostgresStorageLocationRepo";
import { ISupplierRepo } from "../features/supplier/repositories/ISupplierRepo";
import { PostgresSupplierRepo } from "../features/supplier/repositories/PostgresSupplierRepo";
import { IRawMaterialStockRepo } from "../features/raw_material_stock/repositories/IRawMaterialStockRepo";
import { PostgresRawMaterialStockRepo } from "../features/raw_material_stock/repositories/PostgresRawMaterialStockRepo";
import { IAddressRepo } from "../features/address/repositories/IAddressRepo";
import { PostgresAddressRepo } from "../features/address/repositories/PostgresAddressRepo";
import { IPurchaseInvoiceRepo } from "../features/purchase_invoice/repositories/IPurchaseInvoiceRepo";
import { PostgresPurchaseInvoiceRepo } from "../features/purchase_invoice/repositories/PostgresPurchaseInvoiceRepo";
import { IDebitNoteRepo } from "../features/debit_note/repositories/IDebitNoteRepo";
import { PostgresDebitNoteRepo } from "../features/debit_note/repositories/PostgresDebitNoteRepo";
import { ILogRepo } from "../features/logs/repositories/ILogsRepo";
import { PostgresLogsRepo } from "../features/logs/repositories/PostgresLogsRepo";
import { IUserRoleRepo } from "../features/users/sub_feaures/user_roles/repositories/IUserRoleRepo";
import { PostgresUserRoleRepo } from "../features/users/sub_feaures/user_roles/repositories/PostgresUserRoleRepo";
import { INormalUserRepo } from "../features/users/sub_feaures/normal_user/repositories/INormalUserRepo";
import { PostgresNormalUserRepo } from "../features/users/sub_feaures/normal_user/repositories/PostgresNormalUserRepo";

import { IOpeningStockRepo } from '../features/opening_stock/repositories/IOpeningStockRepo'
import { PostgresOpeningStockRepo } from '../features/opening_stock/repositories/PostgresOpeningStockRepo'
import { ICoreUserRepo } from "../features/users/core/repositories/IUserRepo";
import { PostgresCoreUserRepo } from "../features/users/core/repositories/PostgresCoreUserRepo";
import { IRedisServerRepository } from "../features/redis/repositories/IRedisServerRepository";
import { RedisServerRepository } from "../features/redis/repositories/RedisServerRepository";
import { IPurchaseOrderRepo } from "../features/purchase_order/repositories/IPurchaseOrderRepo";
import { PostgresPurchaseOrderRepo } from "../features/purchase_order/repositories/PostgresPurchaseOrderRepo";
import { IDepartmentRepo } from "../features/department/repositories/IDepartmentRepo";
import { PostgresDepartmentRepo } from "../features/department/repositories/PostgresDepartmentRepo";


export class RepoProvider {
  private static _supplierRepo: ISupplierRepo;
  private static _storageLocationRepo: IStorageLocationRepo;
  private static _factoryGateRepo: IFactoryGateRepo;
  private static _rawMaterialRepo: IRawMaterialRepo;
  private static _itemCategoryRepo: IItemCategoryRepo;
  private static _itemAttributeRepo: IItemAttributeRepo;
  private static _itemAttributeValueRepo: IItemAttributeValueRepo;
  private static _itemUnitRepo: IItemUnitRepo;
  private static _rawMaterialStockRepo: IRawMaterialStockRepo;
  private static _addressRepo: IAddressRepo;
  private static _purchaseInvoiceRepo: IPurchaseInvoiceRepo;
  private static _debitNoteRepo: IDebitNoteRepo;
  private static _logRepo: ILogRepo;
  private static _userRoleRepo: IUserRoleRepo;
  private static _coreUserRepo: ICoreUserRepo;
  private static _normalUserRepo: INormalUserRepo;
  private static _openingStockRepo: IOpeningStockRepo;
  private static _redisServerRepository: IRedisServerRepository;
  private static _purchaseOrderRepo: IPurchaseOrderRepo;
  private static _departmentRepo: IDepartmentRepo;




  static get supplierRepo() {
    if (!this._supplierRepo) {
      this._supplierRepo = new PostgresSupplierRepo();
    }
    return this._supplierRepo;
  }


  static get storageLocationRepo() {
    if (!this._storageLocationRepo) {
      this._storageLocationRepo = new PostgresStorageLocationRepo()
    }
    return this._storageLocationRepo
  }

  static get factoryGateRepo() {
    if (!this._factoryGateRepo) {
      this._factoryGateRepo = new PostgresFactoryGateRepo()
    }
    return this._factoryGateRepo
  }


  static get rawMaterialRepo() {
    if (!this._rawMaterialRepo) {
      this._rawMaterialRepo = new PostgresRawMaterialRepo()
    }
    return this._rawMaterialRepo
  }

  static get itemCategoryRepo() {
    if (!this._itemCategoryRepo) {
      this._itemCategoryRepo = new PostgresItemCategoryRepo()
    }
    return this._itemCategoryRepo
  }

  static get itemAttributeRepo() {
    if (!this._itemAttributeRepo) {
      this._itemAttributeRepo = new PostgresItemAttributeRepo()
    }
    return this._itemAttributeRepo
  }

  static get itemAttributeValueRepo() {
    if (!this._itemAttributeValueRepo) {
      this._itemAttributeValueRepo = new PostgresItemAttributeValueRepo()
    }
    return this._itemAttributeValueRepo
  }

  static get itemUnitRepo() {
    if (!this._itemUnitRepo) {
      this._itemUnitRepo = new PostgresItemUnitRepo()
    }
    return this._itemUnitRepo
  }

  static get rawMaterialStockRepo() {
    if (!this._rawMaterialStockRepo) {
      this._rawMaterialStockRepo = new PostgresRawMaterialStockRepo()
    }
    return this._rawMaterialStockRepo
  }

  static get addressRepo() {
    if (!this._addressRepo) {
      this._addressRepo = new PostgresAddressRepo()
    }
    return this._addressRepo
  }

  static get purchaseInvoiceRepo() {
    if (!this._purchaseInvoiceRepo) {
      this._purchaseInvoiceRepo = new PostgresPurchaseInvoiceRepo()
    }
    return this._purchaseInvoiceRepo
  }

  static get debitNoteRepo() {
    if (!this._debitNoteRepo) {
      this._debitNoteRepo = new PostgresDebitNoteRepo()
    }
    return this._debitNoteRepo
  }

  static get logRepo() {
    if (!this._logRepo) {
      this._logRepo = new PostgresLogsRepo();
    }
    return this._logRepo;
  }

  static get userRoleRepo() {
    if (!this._userRoleRepo) {
      this._userRoleRepo = new PostgresUserRoleRepo();
    }
    return this._userRoleRepo;
  }

  static get coreUserRepo() {
    if (!this._coreUserRepo) {
      this._coreUserRepo = new PostgresCoreUserRepo();
    }
    return this._coreUserRepo;
  }

  static get normalUserRepo() {
    if (!this._normalUserRepo) {
      this._normalUserRepo = new PostgresNormalUserRepo();
    }
    return this._normalUserRepo;
  }

  static get openingStockRepo() {
    if (!this._openingStockRepo) {
      this._openingStockRepo = new PostgresOpeningStockRepo()
    }
    return this._openingStockRepo
  }

  static get redisServerRepository() {
    if (!this._redisServerRepository) {
      this._redisServerRepository = new RedisServerRepository()
    }
    return this._redisServerRepository
  }

  static get purchaseOrderRepo () {
    if (!this._purchaseOrderRepo) {
      this._purchaseOrderRepo = new PostgresPurchaseOrderRepo()
      }
      return this._purchaseOrderRepo
  }

  static get departmentRepo() {
    if (!this._departmentRepo) {
      this._departmentRepo = new PostgresDepartmentRepo()
    }
    return this._departmentRepo
  }



}