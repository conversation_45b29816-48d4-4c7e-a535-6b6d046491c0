<script lang="ts">
    import { goto } from "$app/navigation";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import { showErrorToast, showSuccessToast } from "$lib/common/utils/common-utils";

    import { PresenterProvider } from "$lib/PresenterProvider";
    import RawMaterialStockIssuanceForm from "$lib/raw_materia_stock/components/RawMaterialStockIssuanceForm.svelte";
    import type { IRawMaterialStockIssuance } from "$lib/raw_materia_stock/models/IRawMaterialStock";
    import { RawMaterialStockUtils } from "$lib/raw_materia_stock/utils/RawMaterialStockUtils";
    import { onMount } from "svelte";

    let formData: IRawMaterialStockIssuance = RawMaterialStockUtils.getEmptyRawMaterialStockIssuance();
    let originalFormData: IRawMaterialStockIssuance = RawMaterialStockUtils.getEmptyRawMaterialStockIssuance();
    let isDetailPage: boolean = true;
    let isLoadingdata = true;
    let isEditMode: boolean = false;

    const onLoadInitialData = async() => {
        const params = new URLSearchParams(window.location.search);
        const id = params.get("entryId");
        if (!id) {
            return goto("/admin/raw-materials-stock/issuance");
        }
        const res = await PresenterProvider.rawMaterialStockPresenter.getStockIssuanceByEntryId(id);
        if(res.success)
        {
            formData.soNumber = res.data.soNumber;
            formData.notes = res.data.notes;
            formData.issuedToUserId = res.data.issuedTo?.id ?? 0;
            formData.notes = res.data.notes;
            formData.entryId = res.data.entryId;
            formData.issuedAt = res.data.issuedAt;
            formData.issuedBy = res.data.issuedBy;
            formData.issuedTo = res.data.issuedTo;

            formData.rawMaterials = res.data.rawMaterials.map((item)=>{
                return {
                    rawMaterialId: item.rawMaterial.id,
                    name: item.rawMaterial.name,
                    qty: item.qty
                }
            });

            // Store original data for revert functionality
            originalFormData = JSON.parse(JSON.stringify(formData));
            isLoadingdata = false;
        }
    };

    const toggleEditMode = () => {
        if (isEditMode) {
            // If turning off edit mode, revert to original data
            formData = JSON.parse(JSON.stringify(originalFormData));
        }
        isEditMode = !isEditMode;
    };

   

    onMount(() => {
        onLoadInitialData();
    });
</script>

{#if isLoadingdata}
    <PageLoader />
{:else}
    <RawMaterialStockIssuanceForm
        {isDetailPage}
        {formData}
        {isEditMode}
        {toggleEditMode}
    />
{/if}
