<script lang="ts">
    import PurchaseInvoice from "$lib/purchase_invoice/components/PurchaseInvoice.svelte";
    import { userPermissions } from "$lib/common/utils/store";
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
</script>

{#if !$userPermissions.includes(AppPermissions.RAW_MATERIAL_STOCK.RECEIVE)}
    <NoPermissionView />
{:else}
    <PurchaseInvoice  isUserHasEditPermission={true} />
{/if}
