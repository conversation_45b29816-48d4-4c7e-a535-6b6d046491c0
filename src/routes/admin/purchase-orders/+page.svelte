<script lang="ts">
    import NoPermissionView from "$lib/common/components/NoPermissionView.svelte";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import { userPermissions } from "$lib/common/utils/store";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import PurchaseOrderTable from "$lib/purchase-order/components/PurchaseOrderTable.svelte";
    import type { IPurchaseOrder } from "$lib/purchase-order/models/IPurchaseOrder";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { onMount } from "svelte";
    let paginationData: PaginatedDataWrapper<IPurchaseOrder> = {
        pageSize: 10,
        searchText: "",
        pagination: {
            currentPage: 1,
            data: [],
            totalData: 0,
            totalPages: 0,
        },
        onPageChange: () => {},
    };
    let isLoading: boolean = false;
    const loadData = async (page: number) => {
        isLoading = true;

        let response = await PresenterProvider.purchaseOrderPresenter.getAll(
            page,
            paginationData.pageSize
        );
        if (!response.success) {
            return showErrorToast(response.message ?? "error");
        }
        paginationData.pagination = response.data ?? [];
        paginationData.onPageChange = loadData;
        paginationData = paginationData;
        isLoading = false;
    };
    onMount(() => {
        loadData(1);
    });
</script>

<svelte:head><title>Demand Slips</title></svelte:head>

{#if isLoading}
    <PageLoader />
{:else if !$userPermissions.includes(AppPermissions.RAW_MATERIAL_STOCK.READ_PURCHASE)}
    <NoPermissionView />
{:else}
    <PurchaseOrderTable {paginationData} />
{/if}
