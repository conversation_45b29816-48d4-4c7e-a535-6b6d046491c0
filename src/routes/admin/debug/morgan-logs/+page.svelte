<script lang="ts">
    import LogsDashboard from '$lib/components/logs/LogsDashboard.svelte';
    
    let autoRefresh = false;
    let refreshInterval = 30000; // 30 seconds
</script>

<svelte:head>
    <title>Server Logs Dashboard | IMS Admin Panel</title>
    <meta name="description" content="Monitor and analyze server logs with comprehensive statistics and filtering" />
</svelte:head>

<main class="min-h-screen bg-gray-100">
    <div class="container mx-auto py-8">
        <!-- Page header -->
        <div class="mb-8">
            <nav class="text-sm breadcrumbs mb-4">
                <ul class="flex items-center space-x-2 text-gray-500">
                    <li><a href="/" class="hover:text-blue-600">Dashboard</a></li>
                    <li class="before:content-['/'] before:mx-2">System Tools</li>
                    <li class="before:content-['/'] before:mx-2 text-gray-900">Server Logs</li>
                </ul>
            </nav>
        </div>
        
        <!-- Main dashboard component -->
        <LogsDashboard 
            bind:autoRefresh 
            {refreshInterval}
        />
    </div>
</main>

<style>
    /* Custom styles for the logs page */
    .breadcrumbs a {
        transition: color 0.2s ease;
    }
</style> 