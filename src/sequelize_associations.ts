import { AddressTable } from './features/address/database/AddressTable'
import { DebitNoteTable } from './features/debit_note/database/DebitNoteTable'
import { FactoryGateTable } from './features/factory_gates/database/FactoryGateTable'
import { ItemCategoryTable } from './features/item_category/database/ItemCategoryTable'
import { ItemUnitTable } from './features/item_unit/database/ItemUnitTable'
import { LogsTable } from './features/logs/database/LogsTable'
import { PurchaseInvoiceEntryMappingTable } from './features/purchase_invoice/database/PurchaseInvoiceEntryMappingTable'
import { PurchaseInvoiceTable } from './features/purchase_invoice/database/PurchaseInvoiceTable'
import { RawMaterialPriceTable } from './features/raw_material/database/RawMaterialPriceTable'
import { RawMaterialTable } from './features/raw_material/database/RawMaterialTable'
import { RawMaterialHoldTable } from './features/raw_material_stock/database/RawMaterialHoldTable'
import { RawMaterialRejectionTable } from './features/raw_material_stock/database/RawMaterialRejectionTable'
import { RawMaterialStockInTable } from './features/raw_material_stock/database/RawMaterialStockInTable'
import { RawMaterialStockIssuanceTable } from './features/raw_material_stock/database/RawMaterialStockIssuanceTable'
import { RawMaterialStockTable } from './features/raw_material_stock/database/RawMaterialStockTable'
import { StorageLocationTable } from './features/storage_locations/database/StorageLocationTable'
import { SupplierTable } from './features/supplier/database/SupplierTable'
import { CoreUserTable } from './features/users/core/database/CoreUserTable'
import { NormalUserTable } from './features/users/sub_feaures/normal_user/database/NormalUserTable'
import { RolePermissionsTable } from './features/users/sub_feaures/user_permissions/database/RolePermissionsTable'
import { UserRoleTable } from './features/users/sub_feaures/user_roles/database/UserRoleTable'

import { OpeningStockTable } from './features/opening_stock/database/OpeningStockTable'
import { PurchaseOrderTable } from './features/purchase_order/database/PurchaseOrderTable'
import { PurchaseOrderItemTable } from './features/purchase_order/database/PurchaseOrderItemTable'
import { PurchaseInvoicePurchaseOrderMapping } from './features/purchase_invoice/database/PurchaseInvoicePurchaseOrderMapping'
import { RawMaterialExcessEntryTable } from './features/raw_material/database/RawMaterialExcessEntry'
import { RawMaterialReplacementEntryTable } from './features/raw_material/database/RawMaterialReplacementEntry'
import { DepartmentTable } from './features/department/database/DepartmentTable'

export class SequelizeAssociations {
  static async associate() {
    this._rawMaterialAssociations()
    this._rawMaterialStockInAssociations()
    this._rawMaterialStockAssociations()
    this._supplierAssociations()
    this._purchaseInvoiceAssociations()
    this._userTableAssociations()
    this._debitNoteTableAssociations()
    this._logsTableAssociations()
    this._rawMaterialStockIssuanceAssociations()
    this._addressTableAssociations()
    this._userRoleTableAssociations()
    this._openingStockAssociations()
    this._purchaseOrderAssociations()
    this._purchaseOrderItemAssociations()
    this._purchaseOrderAndPurchaseInvoiceAssociations()
    this._rawMaterialExcessEntriesAssociations()
    this._RawMaterialReplacementEntriesAssociations()
    this._departmentAssociations()
  }

  static _rawMaterialAssociations() {
    /* association with ItemUnit */

    ItemUnitTable.hasMany(RawMaterialTable, {
      foreignKey: 'unitId',
      as: 'unit',
    })

    RawMaterialTable.belongsTo(ItemUnitTable, {
      foreignKey: 'unitId',
      as: 'unit',
    })

    /* association with Item Category */
    ItemCategoryTable.hasMany(RawMaterialTable, {
      foreignKey: 'categoryId',
      as: 'category',
    })

    RawMaterialTable.belongsTo(ItemCategoryTable, {
      foreignKey: 'categoryId',
      as: 'category',
    })

    /* association with Price */
    RawMaterialTable.hasMany(RawMaterialPriceTable, {
      foreignKey: 'rawMaterialId',
      as: 'prices',
    })
    RawMaterialPriceTable.belongsTo(RawMaterialTable, {
      foreignKey: 'rawMaterialId',
      as: 'prices',
    })

    SupplierTable.hasMany(RawMaterialPriceTable, {
      foreignKey: 'supplierId',
    })
    RawMaterialPriceTable.belongsTo(SupplierTable, {
      foreignKey: 'supplierId',
      as: 'supplier',
    })

    /* association with raw material rejection */
    RawMaterialTable.hasMany(RawMaterialRejectionTable, {
      foreignKey: 'rawMaterialId',
    })
    RawMaterialRejectionTable.belongsTo(RawMaterialTable, {
      foreignKey: 'rawMaterialId',
    })

    /* association with raw material holds */

    RawMaterialTable.hasMany(RawMaterialHoldTable, {
      foreignKey: 'rawMaterialId',
    })
    RawMaterialHoldTable.belongsTo(RawMaterialTable, {
      foreignKey: 'rawMaterialId',
    })

    /* association with raw material stock issuance */
    RawMaterialTable.hasMany(RawMaterialStockIssuanceTable, {
      foreignKey: 'rawMaterialId',
      as: 'stockIssuances',
    })
    RawMaterialStockIssuanceTable.belongsTo(RawMaterialTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterial',
    })
  }

  static _rawMaterialStockInAssociations() {
    SupplierTable.hasMany(RawMaterialStockInTable, {
      foreignKey: 'supplierId',
    })
    RawMaterialStockInTable.belongsTo(SupplierTable, {
      foreignKey: 'supplierId',
      as: 'supplier',
    })

    StorageLocationTable.hasMany(RawMaterialStockInTable, {
      foreignKey: 'storageLocationId',
      as: 'storageLocation',
    })
    RawMaterialStockInTable.belongsTo(StorageLocationTable, {
      foreignKey: 'storageLocationId',
      as: 'storageLocation',
    })

    FactoryGateTable.hasMany(RawMaterialStockInTable, {
      foreignKey: 'factoryGateId',
    })
    RawMaterialStockInTable.belongsTo(FactoryGateTable, {
      foreignKey: 'factoryGateId',
      as: 'factoryGate',
    })

    RawMaterialTable.hasMany(RawMaterialStockInTable, {
      foreignKey: 'rawMaterialId',
    })
    RawMaterialStockInTable.belongsTo(RawMaterialTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterial',
    })
  }

  static _rawMaterialStockAssociations() {
    RawMaterialTable.hasMany(RawMaterialStockTable, {
      foreignKey: 'rawMaterialId',
    })

    RawMaterialStockTable.belongsTo(RawMaterialTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterial',
    })
  }

  static _supplierAssociations() {
    /* association with Address */
    AddressTable.hasMany(SupplierTable, {
      foreignKey: 'addressId',
    })
    SupplierTable.belongsTo(AddressTable, {
      foreignKey: 'addressId',
      as: 'address',
    })

  }

  static _purchaseInvoiceAssociations() {
    /* association with supplier */

    SupplierTable.hasMany(PurchaseInvoiceTable, {
      foreignKey: 'supplierId',
    })
    PurchaseInvoiceTable.belongsTo(SupplierTable, {
      foreignKey: 'supplierId',
      as: 'supplier',
    })

    /* association with raw material stock in */
    PurchaseInvoiceTable.hasMany(RawMaterialStockInTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'rawMaterialStockInEntries',
    })
    RawMaterialStockInTable.belongsTo(PurchaseInvoiceTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'purchaseInvoice',
    })

    /* association with raw material rejection */
    PurchaseInvoiceTable.hasMany(RawMaterialRejectionTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'rawMaterialRejections',
    })
    RawMaterialRejectionTable.belongsTo(PurchaseInvoiceTable, {
      foreignKey: 'purchaseInvoiceId',
    })

    /* association with raw material hold */
    PurchaseInvoiceTable.hasMany(RawMaterialHoldTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'rawMaterialHolds',
    })
    RawMaterialHoldTable.belongsTo(PurchaseInvoiceTable, {
      foreignKey: 'purchaseInvoiceId',
    })

    /* association with users */

    CoreUserTable.hasMany(PurchaseInvoiceTable, {
      foreignKey: 'purchasedById',
    })
    PurchaseInvoiceTable.belongsTo(CoreUserTable, {
      foreignKey: 'purchasedById',
      as: 'purchasedBy',
    })

    /* with entry mapping */
    PurchaseInvoiceTable.hasOne(PurchaseInvoiceEntryMappingTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'entryMapping',
    })
    PurchaseInvoiceEntryMappingTable.belongsTo(PurchaseInvoiceTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'purchaseInvoice',
    })

    /* with factory Gate */
  }

  static _userTableAssociations() {
    CoreUserTable.hasOne(NormalUserTable, {
      foreignKey: 'coreUserId',
      as: 'normalUser',
    })

    NormalUserTable.belongsTo(CoreUserTable, {
      foreignKey: 'coreUserId',
      as: 'coreUser',
    })

    UserRoleTable.hasMany(CoreUserTable, {
      foreignKey: 'roleId',
    })
    CoreUserTable.belongsTo(UserRoleTable, {
      foreignKey: 'roleId',
      as: 'role',
    })

    // /* with supplier */
    // UserTable.hasMany(SupplierTable, {
    //     foreignKey: 'createdById',
    // });
    // SupplierTable.belongsTo(UserTable, {
    //     foreignKey: 'createdById',
    //     as: "actionUser"
    // });

    // /* with category */
    // UserTable.hasMany(ItemCategoryTable, {
    //     foreignKey: 'createdById',
    // });
    // ItemCategoryTable.belongsTo(UserTable, {
    //     foreignKey: 'createdById',
    //     as: "actionUser"
    // });

    /* with  address*/
    // AddressTable.hasMany(CoreUserTable, {
    //     foreignKey: 'addressId',
    // });
    // CoreUserTable.belongsTo(AddressTable, {
    //     foreignKey: 'addressId',
    //     as: "address",
    // });
  }

  static _debitNoteTableAssociations() {
    /* with purchase invoice */
    PurchaseInvoiceTable.hasMany(DebitNoteTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'debitNotes',
    })
    DebitNoteTable.belongsTo(PurchaseInvoiceTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'purchaseInvoice',
    })

    /* with raw material */
    RawMaterialTable.hasMany(DebitNoteTable, {
      foreignKey: 'rawMaterialId',
    })
    DebitNoteTable.belongsTo(RawMaterialTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterial',
    })
  }

  static _logsTableAssociations() {
    CoreUserTable.hasMany(LogsTable, {
      foreignKey: 'userId',
    })

    LogsTable.belongsTo(CoreUserTable, {
      foreignKey: 'userId',
      as: 'user',
    })
  }

  static _rawMaterialStockIssuanceAssociations() {
    /* association with users */

    CoreUserTable.hasMany(RawMaterialStockIssuanceTable, {
      foreignKey: 'createdById',
    })
    RawMaterialStockIssuanceTable.belongsTo(CoreUserTable, {
      foreignKey: 'createdById',
      as: "issuedBy",
    });

    RawMaterialStockIssuanceTable.belongsTo(CoreUserTable, {
      foreignKey: 'issuedTo',
      as: "issuedToUser",
      });

      CoreUserTable.hasMany(RawMaterialStockIssuanceTable,{
        foreignKey: 'issuedTo',
        as: "rawMaterialIssuance",
      })
  }

  static _addressTableAssociations() {
    AddressTable.hasMany(NormalUserTable, {
      foreignKey: 'addressId',
    })
    NormalUserTable.belongsTo(AddressTable, {
      foreignKey: 'addressId',
      as: 'address',
    })
  }

  static _userRoleTableAssociations() {
    UserRoleTable.hasOne(RolePermissionsTable, {
      foreignKey: 'roleId',
      as: 'permissions',
    })

    RolePermissionsTable.belongsTo(UserRoleTable, {
      foreignKey: 'roleId',
      as: 'role',
    })
  }

  static _openingStockAssociations() {
    RawMaterialTable.hasMany(OpeningStockTable, {
      foreignKey: 'rawMaterialId',
      as: 'openingStocks',
    })
    OpeningStockTable.belongsTo(RawMaterialTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterial',
    })

    CoreUserTable.hasMany(OpeningStockTable, {
      foreignKey: 'createdById',
    })
    OpeningStockTable.belongsTo(CoreUserTable, {
      foreignKey: 'createdById',
      as: 'createdBy',
    })
  }

  static _purchaseOrderAssociations () {
    PurchaseOrderTable.belongsTo(SupplierTable,{
      foreignKey: 'supplierId',
      as: 'supplier',
    })
    SupplierTable.hasMany(PurchaseOrderTable,{
      foreignKey: 'supplierId',
      as: 'purchaseOrder',
    });
    PurchaseOrderTable.belongsTo(CoreUserTable,{
      foreignKey: 'createdById',
      as: 'createdBy',
    })
    CoreUserTable.hasMany(PurchaseOrderTable,{
      foreignKey: 'createdById',
      as: 'purchaseOrder',
    })

    // Department associations
    PurchaseOrderTable.belongsTo(DepartmentTable,{
      foreignKey: 'fromDepartmentId',
      as: 'fromDepartment',
    })
    DepartmentTable.hasMany(PurchaseOrderTable,{
      foreignKey: 'fromDepartmentId',
      as: 'purchaseOrdersFrom',
    });
    PurchaseOrderTable.belongsTo(DepartmentTable,{
      foreignKey: 'toDepartmentId',
      as: 'toDepartment',
    })
    DepartmentTable.hasMany(PurchaseOrderTable,{
      foreignKey: 'toDepartmentId',
      as: 'purchaseOrdersTo',
    });
  }

  static _purchaseOrderItemAssociations () {

    PurchaseOrderTable.hasMany(PurchaseOrderItemTable, {
      foreignKey: 'purchaseOrderId',
      as: 'purchaseOrderItems',
    });

    PurchaseOrderItemTable.belongsTo(PurchaseOrderTable, {
        foreignKey: 'purchaseOrderId',
        as: 'purchaseOrder',
      });
    PurchaseOrderItemTable.belongsTo(RawMaterialTable,{
        foreignKey: 'rawMaterialId',
        as: 'rawMaterial',
        })
    RawMaterialTable.hasMany(PurchaseOrderItemTable,{
          foreignKey: 'rawMaterialId',
          as: 'purchaseOrderItem',
      })
  }

  static _purchaseOrderAndPurchaseInvoiceAssociations() {
    // Mapping table belongs to PurchaseOrder
    PurchaseInvoicePurchaseOrderMapping.belongsTo(PurchaseOrderTable, {
      foreignKey: 'purchaseOrderId',
      as: 'purchaseOrder',
    });
  
    // PurchaseOrder has many mappings
    PurchaseOrderTable.hasMany(PurchaseInvoicePurchaseOrderMapping, {
      foreignKey: 'purchaseOrderId',
      as: 'purchaseInvoiceMappings',
    });
  
    // Mapping table belongs to PurchaseInvoice
    PurchaseInvoicePurchaseOrderMapping.belongsTo(PurchaseInvoiceTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'purchaseInvoice',
    });
  
    // PurchaseInvoice has many mappings
    PurchaseInvoiceTable.hasMany(PurchaseInvoicePurchaseOrderMapping, {
      foreignKey: 'purchaseInvoiceId',
      as: 'purchaseOrderMappings',
    });
  }

  static _rawMaterialExcessEntriesAssociations() {
    RawMaterialExcessEntryTable.belongsTo(RawMaterialTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterial',
      });
      RawMaterialTable.hasMany(RawMaterialExcessEntryTable,{
        foreignKey: 'rawMaterialId',
        as: 'rawMaterialExcessEntry',
    })
    RawMaterialExcessEntryTable.belongsTo(PurchaseInvoiceTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'purchaseInvoice'
    })
    PurchaseInvoiceTable.hasMany(RawMaterialExcessEntryTable,{
      foreignKey: 'purchaseInvoiceId',
      as: 'rawMaterialExcessEntry',
      })
  }

  static _RawMaterialReplacementEntriesAssociations() {
    RawMaterialReplacementEntryTable.belongsTo(RawMaterialTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterial',
      });
      RawMaterialTable.hasMany(RawMaterialReplacementEntryTable,{
        foreignKey: 'rawMaterialId',
        as: 'rawMaterialReplacementEntry',
    })
    RawMaterialReplacementEntryTable.belongsTo(PurchaseInvoiceTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'purchaseInvoice'})
    PurchaseInvoiceTable.hasMany(RawMaterialReplacementEntryTable,{
      foreignKey: 'purchaseInvoiceId',
      as: 'rawMaterialReplacementEntry',
      })
  }

  static _departmentAssociations() {
    DepartmentTable.belongsTo(CoreUserTable, {
      foreignKey: 'createdById',
      as: 'createdBy',
    });

    CoreUserTable.hasMany(DepartmentTable, {
      foreignKey: 'createdById',
      as: 'departments',
    });
  }
}
