import { showErrorToast } from "$lib/common/utils/common-utils";
import { loggedInUser } from "$lib/common/utils/store";
import { RepoProvider } from "$lib/RepoProvider";
import { signOut } from "firebase/auth";
import { usiFirebaseAuth } from "../../firebaseInit";

export const logout = async () => {
    loggedInUser.subscribe(async user => {
        if (user) {
            const res = await RepoProvider.userRepo.logout();
            if (res.success) {
                await signOut(usiFirebaseAuth);
                loggedInUser.update(() => null);
            } else {
                showErrorToast(res.message);
            }
            await signOut(usiFirebaseAuth);
            loggedInUser.update(() => null);
        }
    })
}