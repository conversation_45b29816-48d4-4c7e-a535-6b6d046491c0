import { type D<PERSON>, getHandledErrorDTO, getSuccessDTO, getUnhandledErrorDTO } from "$lib/common/models/BaseDTO";

import type { FetchResult } from "../models/Fetch";
import axios from "axios";
import { RepoProvider } from "$lib/RepoProvider";
import { signOut } from "firebase/auth";
import { usiFirebaseAuth } from "../../../firebaseInit";
import { handleError } from "$lib/common/utils/logging";
import { logout } from "$lib/auth/user-utils";

interface FetchOptions {
    method?: string; // HTTP method (GET, POST, etc.)
    headers?: { [key: string]: string }; // Request headers
    body?: any; // Request body (for POST, PUT, etc.)
}

/**
 * A generic fetch function to make HTTP requests.
 * @param {string} url - The URL to send the request to.
 * @param {FetchOptions} [options={}] - Optional parameters for the fetch request.
 * @param {string | null} [token=null] - Optional Bearer token for authorization.
 * @returns {Promise<DTO<T>>} - A promise that resolves to a DTO containing the response data or error.
 */
async function fetchData<T>(
    url: string,
    options: FetchOptions = {},
    token: string | null = null
): Promise<T> {
    const { method = "GET", headers = {}, body } = options;



        // Set Authorization token
        const accessToken = await getToken(token);

        if (accessToken) {
            headers["Authorization"] = `Bearer ${accessToken}`;

    }


    // Create the config for fetch request
    const config: RequestInit = {
        method,
        headers: {
            "Content-Type": "application/json",
            ...headers,
        },
        body: body ? JSON.stringify(body) : undefined,
    };

    try {
        const response = await fetch(url, config);

        // Check if the response is ok (status in the range 200-299)
        if (!response.ok) {

            /* TODO: self @Mandeep_Singh */
            if (response.status === 401) {
                await logout();
            }


            const errorBody = await response.json().catch(() => response.statusText);
            console.error("HTTP error!", {
                status: response.status,
                message: errorBody,
            });
            return errorBody;
        }

        // Parse the response body as JSON
        const data: T = await response.json();
        return data as T;
    } catch (error: any) {
        return {
            success: false,
            message: error.message,
            data: null,
        } as T;
    }
}

async function fetchDataUsingAxios<T>(
    url: string,
    options: FetchOptions = {},
    token: string | null = null
): Promise<FetchResult<T>> {
    const { method = "POST", headers = {}, body } = options;

    const accessToken = await getToken(token);
    if (accessToken) {
        headers["Authorization"] = `Bearer ${accessToken}`;
    }

    try {
        const response = await axios({
            method,

            url,
            headers: {
                "Content-Type": "multipart/form-data",
                ...headers,
            },
            data: body,
        });

        return {
            success: true,
            message: "Request succeeded",
            data: response.data,
        };
    } catch (error: any) {
        console.error("Axios error!", error);

        const errorMessage = error.response?.data?.message || error.message || "An error occurred";

        return {
            success: false,
            message: errorMessage,
            data: null,
            error: errorMessage, // Provide the error message
        };
    }
}

/**
 * only awaits when token is expired else synchronous
 * @param currToken
 */
const getToken = async (currToken: string | null) => {
    if (currToken) return currToken;

    const newToken = await getUserToken();
    if (!newToken.success) return null;

    return newToken.data;
};


const getUserToken = async () => {
    try {
        const user = usiFirebaseAuth.currentUser;
        if (!user) return getHandledErrorDTO("current user not found in auth");

        const token = await user.getIdToken();

        return getSuccessDTO(token);
    } catch (error: any) {
        handleError(error);
        return getUnhandledErrorDTO(error.message ?? "Error while getting token!", error);
    }
}


export { fetchData, fetchDataUsingAxios, getToken };
