import { LOGS_API_PATH } from '$lib/common/configs/serverConfig';
import { fetchData } from '$lib/fetch/utils/fetch-utils';
import { safeJsonParse, parseApiLogData } from '$lib/common/utils/common-utils';
import type { 
    AvailableLogDatesResponse, 
    PaginatedLogsByDateResponse,
    LogsByDateResponse, 
    LogStatisticsResponse,
    LogFilters,
    IMorganLog
} from '$lib/types/logs';

export class LogsApiService {
    
    /**
     * Fetches all available dates that have log data
     * @returns Promise<AvailableLogDatesResponse>
     * @throws Error if the request fails
     */
    static async getAvailableDates(): Promise<AvailableLogDatesResponse> {
        try {
            const result = await fetchData<AvailableLogDatesResponse>(`${LOGS_API_PATH}/dates`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!result.success) {
                const errorMessage = result.message || 'Failed to fetch available dates';
                if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
                    throw new Error('This endpoint is only available in production environment');
                }
                if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
                    throw new Error('Logs directory not found');
                }
                if (errorMessage.includes('500') || errorMessage.includes('Server Error')) {
                    throw new Error('Internal server error occurred');
                }
                throw new Error(errorMessage);
            }

            return result;
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error('Network error: Unable to connect to the logs server');
        }
    }

    /**
     * Fetches paginated log entries for a specific date with filtering
     * @param date - Date string in YYYY-MM-DD format
     * @param page - Page number (default: 1)
     * @param pageSize - Number of logs per page (default: 50, max: 1000)
     * @param filters - Optional filters for the logs
     * @returns Promise<PaginatedLogsByDateResponse>
     * @throws Error if the request fails or date is invalid
     */
    static async getPaginatedLogsByDate(
        date: string, 
        page: number = 1, 
        pageSize: number = 50, 
        filters?: LogFilters
    ): Promise<PaginatedLogsByDateResponse> {
        // Validate date format
        if (!this.isValidDateFormat(date)) {
            throw new Error('Invalid date format. Use YYYY-MM-DD');
        }

        // Validate pagination parameters
        if (page < 1) {
            throw new Error('Page must be a positive number');
        }
        if (pageSize < 1 || pageSize > 1000) {
            throw new Error('Page size must be between 1 and 1000');
        }

        try {
            // Build query parameters
            const params = new URLSearchParams({
                date,
                page: page.toString(),
                pageSize: pageSize.toString(),
            });

            // Add filter parameters if provided
            if (filters) {
                if (filters.method) params.append('method', filters.method);
                if (filters.statusCode) params.append('statusCode', filters.statusCode.toString());
                if (filters.hasError !== undefined) params.append('hasError', filters.hasError.toString());
                if (filters.url) params.append('url', filters.url);
                if (filters.ip) params.append('ip', filters.ip);
            }

            const result = await fetchData<PaginatedLogsByDateResponse>(
                `${LOGS_API_PATH}/by-date?${params.toString()}`, 
                {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );

            if (!result.success) {
                const errorMessage = result.message || 'Failed to fetch logs';
                if (errorMessage.includes('400') || errorMessage.includes('Bad Request')) {
                    throw new Error('Invalid parameters or missing date parameter');
                }
                if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
                    throw new Error('This endpoint is only available in production environment');
                }
                if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
                    throw new Error(`No log files found for date: ${date}`);
                }
                if (errorMessage.includes('500') || errorMessage.includes('Server Error')) {
                    throw new Error('Internal server error occurred');
                }
                throw new Error(errorMessage);
            }

            // Parse JSON strings in the logs if needed
            if (result.data && result.data.logs) {
                result.data.logs = result.data.logs.map((log: IMorganLog) => parseApiLogData(log));
            }

            return result;
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error('Network error: Unable to connect to the logs server');
        }
    }

    /**
     * Fetches all log entries for a specific date (legacy method)
     * @param date - Date string in YYYY-MM-DD format
     * @returns Promise<LogsByDateResponse>
     * @throws Error if the request fails or date is invalid
     */
    static async getLogsByDate(date: string): Promise<LogsByDateResponse> {
        // Validate date format
        if (!this.isValidDateFormat(date)) {
            throw new Error('Invalid date format. Use YYYY-MM-DD');
        }

        try {
            const result = await fetchData<LogsByDateResponse>(`${LOGS_API_PATH}/by-date/all?date=${encodeURIComponent(date)}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!result.success) {
                const errorMessage = result.message || 'Failed to fetch logs';
                if (errorMessage.includes('400') || errorMessage.includes('Bad Request')) {
                    throw new Error('Invalid date format or missing date parameter');
                }
                if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
                    throw new Error('This endpoint is only available in production environment');
                }
                if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
                    throw new Error(`No log files found for date: ${date}`);
                }
                if (errorMessage.includes('500') || errorMessage.includes('Server Error')) {
                    throw new Error('Internal server error occurred');
                }
                throw new Error(errorMessage);
            }

            // Parse JSON strings in the logs if needed
            if (result.data) {
                result.data = result.data.map((log: IMorganLog) => parseApiLogData(log));
            }

            return result;
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error('Network error: Unable to connect to the logs server');
        }
    }

    /**
     * Fetches comprehensive statistics for logs on a specific date
     * @param date - Date string in YYYY-MM-DD format
     * @returns Promise<LogStatisticsResponse>
     * @throws Error if the request fails or date is invalid
     */
    static async getStatsByDate(date: string): Promise<LogStatisticsResponse> {
        // Validate date format
        if (!this.isValidDateFormat(date)) {
            throw new Error('Invalid date format. Use YYYY-MM-DD');
        }

        try {
            const result = await fetchData<LogStatisticsResponse>(`${LOGS_API_PATH}/stats-by-date?date=${encodeURIComponent(date)}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!result.success) {
                const errorMessage = result.message || 'Failed to fetch statistics';
                if (errorMessage.includes('400') || errorMessage.includes('Bad Request')) {
                    throw new Error('Invalid date format or missing date parameter');
                }
                if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
                    throw new Error('This endpoint is only available in production environment');
                }
                if (errorMessage.includes('404') || errorMessage.includes('Not Found')) {
                    throw new Error(`No log files found for date: ${date}`);
                }
                if (errorMessage.includes('500') || errorMessage.includes('Server Error')) {
                    throw new Error('Internal server error occurred');
                }
                throw new Error(errorMessage);
            }

            return result;
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error('Network error: Unable to connect to the logs server');
        }
    }

    /**
     * Fetches both paginated logs and statistics for a date simultaneously
     * @param date - Date string in YYYY-MM-DD format
     * @param page - Page number (default: 1)
     * @param pageSize - Number of logs per page (default: 50)
     * @param filters - Optional filters for the logs
     * @returns Promise<{logs: PaginatedLogsByDateResponse, stats: LogStatisticsResponse}>
     */
    static async getPaginatedLogsAndStatsByDate(
        date: string, 
        page: number = 1, 
        pageSize: number = 50, 
        filters?: LogFilters
    ): Promise<{
        logs: PaginatedLogsByDateResponse;
        stats: LogStatisticsResponse;
    }> {
        try {
            const [logs, stats] = await Promise.all([
                this.getPaginatedLogsByDate(date, page, pageSize, filters),
                this.getStatsByDate(date)
            ]);

            return { logs, stats };
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error('Failed to fetch logs and statistics');
        }
    }

    /**
     * Fetches both logs and statistics for a date simultaneously (legacy method)
     * @param date - Date string in YYYY-MM-DD format
     * @returns Promise<{logs: LogsByDateResponse, stats: LogStatisticsResponse}>
     */
    static async getLogsAndStatsByDate(date: string): Promise<{
        logs: LogsByDateResponse;
        stats: LogStatisticsResponse;
    }> {
        try {
            const [logs, stats] = await Promise.all([
                this.getLogsByDate(date),
                this.getStatsByDate(date)
            ]);

            return { logs, stats };
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error('Failed to fetch logs and statistics');
        }
    }

    /**
     * Validates date format (YYYY-MM-DD)
     * @param date - Date string to validate
     * @returns boolean - true if valid format
     */
    private static isValidDateFormat(date: string): boolean {
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(date)) {
            return false;
        }

        // Check if the date is actually valid
        const parsedDate = new Date(date);
        const [year, month, day] = date.split('-').map(Number);
        
        return (
            parsedDate.getFullYear() === year &&
            parsedDate.getMonth() === month - 1 &&
            parsedDate.getDate() === day
        );
    }

    /**
     * Checks if the logs API is available
     * @returns Promise<boolean>
     */
    static async isLogsApiAvailable(): Promise<boolean> {
        try {
            await this.getAvailableDates();
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * Gets today's date in YYYY-MM-DD format
     * @returns string - Today's date
     */
    static getTodayDate(): string {
        return new Date().toISOString().split('T')[0];
    }

    /**
     * Gets yesterday's date in YYYY-MM-DD format
     * @returns string - Yesterday's date
     */
    static getYesterdayDate(): string {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        return yesterday.toISOString().split('T')[0];
    }
} 