enum USER_ROLE_PERMISSIONS {
    CREATE = 'create#user_role',
    READ = 'read#user_role',
    UPDATE = 'update#user_role',
    // DELETE = 'delete_user_role',
}

enum USER_PERMISSIONS {
    CREATE = 'create#user',
    READ = 'read#user',
    UPDATE = 'update#user',
    PASSWORD_RESET = 'password_reset#user',
    // DELETE = 'delete_user',
}

enum SUPPLIER_PERMISSIONS {
    CREATE = 'create#supplier',
    READ = 'read#supplier',
    UPDATE = 'update#supplier',
    // DELETE = 'delete_supplier',
}
enum STORAGE_LOCATION_PERMISSIONS {
    CREATE = 'create#storage_location',
    READ = 'read#storage_location',
    UPDATE = 'update#storage_location',
    // DELETE = 'delete_storage_location',
}

enum FACTORY_GATE_PERMISSIONS {
    CREATE = 'create#factory_gate',
    READ = 'read#factory_gate',
    UPDATE = 'update#factory_gate',
    // DELETE = 'delete_factory_gate',
}

enum ITEM_CATEGORY_PERMISSIONS {
    CREATE = 'create#item_category',
    READ = 'read#item_category',
    UPDATE = 'update#item_category',
    // DELETE = 'delete_item_category',
}

enum ITEM_UNIT_PERMISSIONS {
    CREATE = 'create#item_unit',
    READ = 'read#item_unit',
    UPDATE = 'update#item_unit',
    // DELETE = 'delete_item_unit',
}

enum RAW_MATERIAL_PERMISSIONS {
    CREATE = 'create#raw_material',
    READ = 'read#raw_material',
    UPDATE = 'update#raw_material',
    // DELETE = 'delete_raw_material',
}

enum RAW_MATERIAL_STOCK_PERMISSIONS {
    RECEIVE = 'receive_stock#raw_material_stock',
    READ_CURRENT_STOCK = 'read_current_stock#raw_material_stock',
    // UPDATE_CURRENT_STOCK = 'update_current_stock#raw_material_stock',
    // READ_OPENING_STOCK = 'read_opening_stock#raw_material_stock',
    // ADD_OPENING_STOCK = 'add_opening_stock#raw_material_stock',
    // UPDATE_OPENING_STOCK = 'update_opening_stock#raw_material_stock',
    READ_STOCK_IN = 'read_stock_in#raw_material_stock',
    ASSIGN_STORAGE = 'assign_storage#raw_material_stock',
    READ_ISSUANCE = 'read_issuance#raw_material_stock',
    CREATE_ISSUANCE = 'create_issuance#raw_material_stock',
    READ_PURCHASE = 'read_purchase#raw_material_stock',
    UPDATE_PURCHASE = 'update_purchase#raw_material_stock',
    UPDATE_PURCHASE_FULL = 'update_purchase_full#raw_material_stock',
    // DELETE = 'delete_raw_material_stock',
}

enum DEBIT_NOTE_PERMISSIONS {
    CREATE = 'create#debit_note',
    READ = 'read#debit_note',
    UPDATE = 'update#debit_note',
    PRINT = 'print#debit_note',
    // DELETE = 'delete_user',
}

enum LOG_PERMISSIONS {
    READ = 'read#log',
}

enum STOCK_ADJUSTMENT_PERMISSIONS {
    CREATE = 'create#stock_adjustment',
    READ = 'read#stock_adjustment',
    UPDATE = 'update#stock_adjustment',
    // DELETE = 'delete_stock_adjustment',
}



const PERMISSION_RELATIONSHIPS: {
    [key: string]: {
        dependencies: {
            displayName: string;
            permission: string;
        }[];
        transitiveDependencies: {
            displayName: string;
            permission: string;
        }[];
    };
} = {
    /* user role permissions */
    [USER_ROLE_PERMISSIONS.UPDATE]: {
        dependencies: [
            { displayName: 'Read User Role', permission: USER_ROLE_PERMISSIONS.READ },
        ],
        transitiveDependencies: [],
    },
    [USER_ROLE_PERMISSIONS.READ]: {
        dependencies: [],
        transitiveDependencies: [
            { displayName: 'Create User', permission: USER_PERMISSIONS.CREATE },
            { displayName: 'Update User', permission: USER_PERMISSIONS.UPDATE },
            { displayName: 'Update User Role', permission: USER_ROLE_PERMISSIONS.UPDATE },
        ],
    },

    /* user permissions */
    [USER_PERMISSIONS.UPDATE]: {
        dependencies: [{ displayName: 'Read User', permission: USER_PERMISSIONS.READ }],
        transitiveDependencies: [],
    },
    [USER_PERMISSIONS.READ]: {
        dependencies: [],
        transitiveDependencies: [
            {
                displayName: 'Update User',
                permission: USER_PERMISSIONS.UPDATE
            },
            {
                displayName: 'Receive Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.RECEIVE,
            },
            {
                displayName: 'Update Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.UPDATE_PURCHASE,
            }
        ],
    },
    [USER_PERMISSIONS.CREATE]: {
        dependencies: [
            { displayName: 'Read User Role', permission: USER_ROLE_PERMISSIONS.READ },
        ],
        transitiveDependencies: [
            {
                displayName: 'Receive Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.RECEIVE,
            },
            {
                displayName: 'Update Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.UPDATE_PURCHASE,
            }
        ],
    },

    /* supplier permissions */
    [SUPPLIER_PERMISSIONS.UPDATE]: {
        dependencies: [
            { displayName: 'Read Supplier', permission: SUPPLIER_PERMISSIONS.READ },
        ],
        transitiveDependencies: [],
    },
    [SUPPLIER_PERMISSIONS.READ]: {
        dependencies: [],
        transitiveDependencies: [
            {
                displayName: 'Update Supplier',
                permission: SUPPLIER_PERMISSIONS.UPDATE,
            },
            {
                displayName: 'Create Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.CREATE
            },
            {
                displayName: 'Update Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.UPDATE
            },
            {
                displayName: 'Receive Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.RECEIVE,
            },
            {
                displayName: 'Update Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.UPDATE_PURCHASE,
            },
        ],
    },
    [SUPPLIER_PERMISSIONS.CREATE]: {
        dependencies: [],
        transitiveDependencies: [
            {
                displayName: 'Create Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.CREATE
            },
            {
                displayName: 'Update Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.UPDATE
            },
            {
                displayName: 'Receive Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.RECEIVE,
            },
            {
                displayName: 'Update Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.UPDATE_PURCHASE,
            },
        ],
    },

    /* storage location permissions */
    [STORAGE_LOCATION_PERMISSIONS.UPDATE]: {
        dependencies: [
            {
                displayName: 'Read Storage Location',
                permission: STORAGE_LOCATION_PERMISSIONS.READ,
            },
        ],
        transitiveDependencies: [],
    },
    [STORAGE_LOCATION_PERMISSIONS.READ]: {
        dependencies: [],
        transitiveDependencies: [
            {
                displayName: 'Update Storage Location',
                permission: STORAGE_LOCATION_PERMISSIONS.UPDATE,
            },
            {
                displayName: 'Assign storage to Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.ASSIGN_STORAGE,
            }
        ],
    },
    [STORAGE_LOCATION_PERMISSIONS.CREATE]: {
        dependencies: [],
        transitiveDependencies: [
            {
                displayName: 'Assign storage to Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.ASSIGN_STORAGE,
            }
        ],
    },

    /* factory gate permissions */
    [FACTORY_GATE_PERMISSIONS.UPDATE]: {
        dependencies: [
            {
                displayName: 'Read Factory Gate',
                permission: FACTORY_GATE_PERMISSIONS.READ,
            },
        ],
        transitiveDependencies: [],
    },
    [FACTORY_GATE_PERMISSIONS.CREATE]: {
        dependencies: [
            {
                displayName: 'Receive Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.RECEIVE,
            },
            {
                displayName: 'Update Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.UPDATE_PURCHASE,
            }
        ],
        transitiveDependencies: [],
    },
    [FACTORY_GATE_PERMISSIONS.READ]: {
        dependencies: [],
        transitiveDependencies: [

            {
                displayName: 'Update Factory Gate',
                permission: FACTORY_GATE_PERMISSIONS.UPDATE,
            },
            {
                displayName: 'Receive Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.RECEIVE,
            },
            {
                displayName: 'Update Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.UPDATE_PURCHASE,
            }
        ],
    },

    /* item category permissions */
    [ITEM_CATEGORY_PERMISSIONS.UPDATE]: {
        dependencies: [
            {
                displayName: 'Read Item Category',
                permission: ITEM_CATEGORY_PERMISSIONS.READ,
            },
        ],
        transitiveDependencies: [],
    },
    [ITEM_CATEGORY_PERMISSIONS.READ]: {
        dependencies: [],
        transitiveDependencies: [
            {
                displayName: 'Create Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.CREATE
            },
            {
                displayName: 'Update Item Category',
                permission: ITEM_CATEGORY_PERMISSIONS.UPDATE
            },
            {
                displayName: 'Update Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.UPDATE
            },
            {
                displayName: 'Receive Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.RECEIVE,
            },
            {
                displayName: 'Update Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.UPDATE_PURCHASE,
            },
            {
                displayName: 'Read Current Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.READ_CURRENT_STOCK,
            },
        ],
    },
    [ITEM_CATEGORY_PERMISSIONS.CREATE]: {
        dependencies: [],
        transitiveDependencies: [
            {
                displayName: 'Create Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.CREATE
            },
            {
                displayName: 'Update Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.UPDATE
            },
            {
                displayName: 'Receive Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.RECEIVE,
            },
            {
                displayName: 'Update Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.UPDATE_PURCHASE,
            },
        ],
    },

    /* item unit permissions */
    [ITEM_UNIT_PERMISSIONS.UPDATE]: {
        dependencies: [
            { displayName: 'Read Item Unit', permission: ITEM_UNIT_PERMISSIONS.READ },
        ],
        transitiveDependencies: [],
    },
    [ITEM_UNIT_PERMISSIONS.READ]: {
        dependencies: [],
        transitiveDependencies: [
            {
                displayName: 'Create Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.CREATE
            },
            {
                displayName: 'Update Item Unit',
                permission: ITEM_UNIT_PERMISSIONS.UPDATE
            },
            {
                displayName: 'Update Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.UPDATE
            },
            {
                displayName: 'Receive Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.RECEIVE,
            },
            {
                displayName: 'Update Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.UPDATE_PURCHASE,
            },
        ],
    },
    [ITEM_UNIT_PERMISSIONS.CREATE]: {
        dependencies: [],
        transitiveDependencies: [
            {
                displayName: 'Create Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.CREATE
            },
            {
                displayName: 'Update Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.UPDATE
            },
            {
                displayName: 'Receive Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.RECEIVE,
            },
            {
                displayName: 'Update Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.UPDATE_PURCHASE,
            },
        ],
    },

    /* raw material permissions */
    [RAW_MATERIAL_PERMISSIONS.UPDATE]: {
        dependencies: [
            {
                displayName: 'Read Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.READ,
            },
            {
                displayName: 'Read Item Category',
                permission: ITEM_CATEGORY_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Item Category',
                permission: ITEM_CATEGORY_PERMISSIONS.CREATE,
            },
            { displayName: 'Read Item Unit', permission: ITEM_UNIT_PERMISSIONS.READ },
            {
                displayName: 'Create Item Unit',
                permission: ITEM_UNIT_PERMISSIONS.CREATE,
            },
            { displayName: 'Read Supplier', permission: SUPPLIER_PERMISSIONS.READ },
            {
                displayName: 'Create Supplier',
                permission: SUPPLIER_PERMISSIONS.CREATE,
            },
        ],
        transitiveDependencies: [],
    },
    [RAW_MATERIAL_PERMISSIONS.READ]: {
        dependencies: [],
        transitiveDependencies: [
            {
                displayName: 'Update Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.UPDATE,
            },
            {
                displayName: 'Receive Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.RECEIVE,
            },
            {
                displayName: 'Update Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.UPDATE_PURCHASE,
            },
            {
                displayName: 'Read Current Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.READ_CURRENT_STOCK,
            },
            {
                displayName: 'Read Stock In',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.READ_STOCK_IN,
            },
            {
                displayName: 'Read Issuance',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.READ_ISSUANCE,
            },
            {
                displayName: 'Create Issuance',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.CREATE_ISSUANCE,
            },
            {
                displayName: 'Read Purchase',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.READ_PURCHASE,
            },
            {
                displayName: 'Create Stock Adjustments',
                permission: STOCK_ADJUSTMENT_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Update Stock Adjustments',
                permission: STOCK_ADJUSTMENT_PERMISSIONS.UPDATE,
            },
            {
                displayName: 'Read Stock Adjustments',
                permission: STOCK_ADJUSTMENT_PERMISSIONS.READ,
            },
        ],
    },
    [RAW_MATERIAL_PERMISSIONS.CREATE]: {
        dependencies: [
            {
                displayName: 'Read Item Category',
                permission: ITEM_CATEGORY_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Item Category',
                permission: ITEM_CATEGORY_PERMISSIONS.CREATE,
            },
            { displayName: 'Read Item Unit', permission: ITEM_UNIT_PERMISSIONS.READ },
            {
                displayName: 'Create Item Unit',
                permission: ITEM_UNIT_PERMISSIONS.CREATE,
            },
            { displayName: 'Read Supplier', permission: SUPPLIER_PERMISSIONS.READ },
            {
                displayName: 'Create Supplier',
                permission: SUPPLIER_PERMISSIONS.CREATE,
            },
        ],
        transitiveDependencies: [
            {
                displayName: 'Receive Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.RECEIVE,
            },
            {
                displayName: 'Update Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.UPDATE_PURCHASE,
            },
        ],
    },

    /* raw material stock permissions */
    [RAW_MATERIAL_STOCK_PERMISSIONS.RECEIVE]: {
        dependencies: [
            { displayName: 'Read Supplier', permission: SUPPLIER_PERMISSIONS.READ },
            { displayName: 'Create Supplier', permission: SUPPLIER_PERMISSIONS.CREATE },
            {
                displayName: 'Read Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Read Factory Gate',
                permission: FACTORY_GATE_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Factory Gate',
                permission: FACTORY_GATE_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Read User',
                permission: USER_PERMISSIONS.READ,
            },
            {
                displayName: 'Create User',
                permission: USER_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Read Item Category',
                permission: ITEM_CATEGORY_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Item Category',
                permission: ITEM_CATEGORY_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Read Item Unit',
                permission: ITEM_UNIT_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Item Unit',
                permission: ITEM_UNIT_PERMISSIONS.CREATE,
            },
        ],
        transitiveDependencies: [],
    },
    [RAW_MATERIAL_STOCK_PERMISSIONS.UPDATE_PURCHASE]: {
        dependencies: [
            { displayName: 'Read Supplier', permission: SUPPLIER_PERMISSIONS.READ },
            { displayName: 'Create Supplier', permission: SUPPLIER_PERMISSIONS.CREATE },
            {
                displayName: 'Read Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Read Factory Gate',
                permission: FACTORY_GATE_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Factory Gate',
                permission: FACTORY_GATE_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Read User',
                permission: USER_PERMISSIONS.READ,
            },
            {
                displayName: 'Create User',
                permission: USER_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Read Item Category',
                permission: ITEM_CATEGORY_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Item Category',
                permission: ITEM_CATEGORY_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Read Item Unit',
                permission: ITEM_UNIT_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Item Unit',
                permission: ITEM_UNIT_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Receive Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.RECEIVE,
            },
            {
                displayName: 'Read Purchase',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.READ_PURCHASE,
            },
        ],
        transitiveDependencies: [],
    },
    [RAW_MATERIAL_STOCK_PERMISSIONS.UPDATE_PURCHASE_FULL]: {
        dependencies: [
            { displayName: 'Read Supplier', permission: SUPPLIER_PERMISSIONS.READ },
            { displayName: 'Create Supplier', permission: SUPPLIER_PERMISSIONS.CREATE },
            {
                displayName: 'Read Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Read Factory Gate',
                permission: FACTORY_GATE_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Factory Gate',
                permission: FACTORY_GATE_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Read User',
                permission: USER_PERMISSIONS.READ,
            },
            {
                displayName: 'Create User',
                permission: USER_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Read Item Category',
                permission: ITEM_CATEGORY_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Item Category',
                permission: ITEM_CATEGORY_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Read Item Unit',
                permission: ITEM_UNIT_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Item Unit',
                permission: ITEM_UNIT_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Receive Raw Material Stock',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.RECEIVE,
            },
            {
                displayName: 'Read Purchase',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.READ_PURCHASE,
            },
            {
                displayName: 'Update Purchase',
                permission: RAW_MATERIAL_STOCK_PERMISSIONS.UPDATE_PURCHASE,
            }
        ],
        transitiveDependencies: [],
    },
    [RAW_MATERIAL_STOCK_PERMISSIONS.READ_CURRENT_STOCK]: {
        dependencies: [
            { displayName: 'Read Raw Material', permission: RAW_MATERIAL_PERMISSIONS.READ },
            {
                displayName: 'Read Item Categories',
                permission: ITEM_CATEGORY_PERMISSIONS.READ,
            }
        ],
        transitiveDependencies: [


        ],
    },
    [RAW_MATERIAL_STOCK_PERMISSIONS.READ_STOCK_IN]: {
        dependencies: [
            { displayName: 'Read Raw Material', permission: RAW_MATERIAL_PERMISSIONS.READ },
        ],
        transitiveDependencies: [],
    },
    [RAW_MATERIAL_STOCK_PERMISSIONS.ASSIGN_STORAGE]: {
        dependencies: [
            { displayName: 'Read Storage Location', permission: STORAGE_LOCATION_PERMISSIONS.READ },
            { displayName: 'Create Storage Location', permission: STORAGE_LOCATION_PERMISSIONS.CREATE },
        ],
        transitiveDependencies: [],
    },
    [RAW_MATERIAL_STOCK_PERMISSIONS.READ_ISSUANCE]: {
        dependencies: [
            { displayName: 'Read Raw Material', permission: RAW_MATERIAL_PERMISSIONS.READ },
        ],
        transitiveDependencies: [],
    },
    [RAW_MATERIAL_STOCK_PERMISSIONS.CREATE_ISSUANCE]: {
        dependencies: [
            { displayName: 'Read Raw Material', permission: RAW_MATERIAL_PERMISSIONS.READ },
        ],
        transitiveDependencies: [],
    },
    [RAW_MATERIAL_STOCK_PERMISSIONS.READ_PURCHASE]: {
        dependencies: [

            { displayName: 'Read Supplier', permission: SUPPLIER_PERMISSIONS.READ },
            { displayName: 'Create Supplier', permission: SUPPLIER_PERMISSIONS.CREATE },
            {
                displayName: 'Read Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Read Factory Gate',
                permission: FACTORY_GATE_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Factory Gate',
                permission: FACTORY_GATE_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Read User',
                permission: USER_PERMISSIONS.READ,
            },
            {
                displayName: 'Create User',
                permission: USER_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Read Item Category',
                permission: ITEM_CATEGORY_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Item Category',
                permission: ITEM_CATEGORY_PERMISSIONS.CREATE,
            },
            {
                displayName: 'Read Item Unit',
                permission: ITEM_UNIT_PERMISSIONS.READ,
            },
            {
                displayName: 'Create Item Unit',
                permission: ITEM_UNIT_PERMISSIONS.CREATE,
            },
        ],
        transitiveDependencies: [],
    },

    /* debit note permissions */
    [DEBIT_NOTE_PERMISSIONS.UPDATE]: {
        dependencies: [
            { displayName: 'Read Debit Note', permission: DEBIT_NOTE_PERMISSIONS.READ },
        ],
        transitiveDependencies: [],
    },
    [DEBIT_NOTE_PERMISSIONS.READ]: {
        dependencies: [],
        transitiveDependencies: [

            {
                displayName: 'Update Debit Note',
                permission: DEBIT_NOTE_PERMISSIONS.UPDATE,
            }
        ],
    },

    /* Operation stock permissions */
    [STOCK_ADJUSTMENT_PERMISSIONS.CREATE]: {
        dependencies: [
            {
                displayName: 'Read Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.READ,
            },
        ],
        transitiveDependencies: [],
    },
    [STOCK_ADJUSTMENT_PERMISSIONS.UPDATE]: {
        dependencies: [
            {
                displayName: 'Read Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.READ,
            },
            {
                displayName: 'Read Stock Adjustment',
                permission: STOCK_ADJUSTMENT_PERMISSIONS.READ,
            },

        ],
        transitiveDependencies: [],
    },
    [STOCK_ADJUSTMENT_PERMISSIONS.READ]: {
        dependencies: [
            {
                displayName: 'Read Raw Material',
                permission: RAW_MATERIAL_PERMISSIONS.READ,
            },
        ],
        transitiveDependencies: [

            {
                displayName: 'Update Stock Adjustment',
                permission: STOCK_ADJUSTMENT_PERMISSIONS.UPDATE,
            }
        ],
    },
};

abstract class AppPermissions {
    static USER_ROLE = USER_ROLE_PERMISSIONS;
    static USER = USER_PERMISSIONS;
    static SUPPLIER = SUPPLIER_PERMISSIONS;
    static STORAGE_LOCATION = STORAGE_LOCATION_PERMISSIONS;
    static FACTORY_GATE = FACTORY_GATE_PERMISSIONS;
    static ITEM_CATEGORY = ITEM_CATEGORY_PERMISSIONS;
    static ITEM_UNIT = ITEM_UNIT_PERMISSIONS;
    static RAW_MATERIAL = RAW_MATERIAL_PERMISSIONS;
    static RAW_MATERIAL_STOCK = RAW_MATERIAL_STOCK_PERMISSIONS;
    static DEBIT_NOTE = DEBIT_NOTE_PERMISSIONS;
    static LOG = LOG_PERMISSIONS;
    static STOCK_ADJUSTMENT = STOCK_ADJUSTMENT_PERMISSIONS;
}

export {
    USER_ROLE_PERMISSIONS,
    USER_PERMISSIONS,
    SUPPLIER_PERMISSIONS,
    STORAGE_LOCATION_PERMISSIONS,
    FACTORY_GATE_PERMISSIONS,
    ITEM_CATEGORY_PERMISSIONS,
    ITEM_UNIT_PERMISSIONS,
    RAW_MATERIAL_PERMISSIONS,
    RAW_MATERIAL_STOCK_PERMISSIONS,
    DEBIT_NOTE_PERMISSIONS,
    LOG_PERMISSIONS,
    AppPermissions,
    STOCK_ADJUSTMENT_PERMISSIONS,
    PERMISSION_RELATIONSHIPS,
};
