import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { IUser, IUserCreateRequest, IUserUpdateRequest, ResetPasswordPayload } from "../models/User";

export interface IUserRepo {
  getById(id: number, token?: string): Promise<DTO<IUser>>;
  saveUser(user: IUserCreateRequest): Promise<DTO<IUser>>;
  updateUser(user: IUserUpdateRequest): Promise<DTO<null>>;
  getAll(page: number, pageSize: number, text?: string): Promise<DTO<PaginatedBaseResponse<IUser>>>;
  getByFirebaseToken(): Promise<DTO<IUser>>;
  searchByText(text: string): Promise<DTO<PaginatedBaseResponse<IUser>>>;
  resetPassword(payload: ResetPasswordPayload): Promise<DTO<null>>
  logout(): Promise<DTO<boolean>>
}
