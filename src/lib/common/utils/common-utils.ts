import { Timestamp } from "firebase/firestore";
import { v4 as uuidv4 } from "uuid";
import { notyf } from "../configs/notyf";

const parseDate = (val: Date) => {
    let dVal: Date = new Timestamp(
        //@ts-ignore
        val?.seconds,
        //@ts-ignore
        val?.nanoseconds
    ).toDate();

    // Get the day (abbreviated)
    const day = dVal.toLocaleDateString("en-US", { weekday: "short" });

    // Get the month (abbreviated)
    const month = dVal.toLocaleDateString("en-US", { month: "short" });

    // Get the date
    const dateNumber = dVal.getDate();

    // Get the formatted time
    const formattedTime = dVal.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
    });

    // Concatenate the parts to form the final string
    return `${day}, ${month} ${dateNumber} ${dVal.getFullYear()}, ${formattedTime}`;
};

const getUid = () => {
    let newId = uuidv4();
    return newId;
};

function debounce<T extends (...args: any[]) => any>(callback: T, delay: number) {
    let timeoutId: NodeJS.Timeout;

    return (...args: Parameters<T>) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => callback(...args), delay);
    };
}

/**
 * Converts a Timestamp or Date object obtained from Firebase into a Date object.
 * This function is designed to be used internally within the repository
 * when retrieving data from Firebase, where interfaces expect Date types,
 * but Firebase get functions return Timestamp types.
 *
 * Also converts Invalid Date obj to good Date Object
 *
 * @param {Timestamp | Date} date - The input Timestamp or Date object to be converted.
 * @returns {Date} - The converted Date object.
 */
const getDateFromTimeStamp = (date: Timestamp | Date): Date => {
    if (isTimeStamp(date)) {
        return new Timestamp(date.seconds, date.nanoseconds).toDate();
    }

    let resDate = new Date(date);
    if (isNaN(resDate.getDate())) return new Date();

    return resDate;
};

/**
 * Converts a Date object to a string in the format YYYY-MM-DD.
 *
 * @param date - The Date object to convert.
 * @returns A string representing the date in the format YYYY-MM-DD.
 */
const formatDateToInput = (date: Date): string => {
    if (!date) return "";
    date = new Date(date);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero-based
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
};

/**
 * Checks if the provided data is of Timestamp [bulkType].
 * This function is intended to be used in conjunction with `getDateFromTimeStamp`.
 * @param {Timestamp | Date} date - The input data to be checked.
 * @returns {boolean} - Returns true if the input data is of Timestamp [bulkType], false otherwise.
 */
const isTimeStamp = (date: Timestamp | Date): date is Timestamp => {
    return (
        (date as Timestamp).nanoseconds !== undefined && (date as Timestamp).seconds !== undefined
    );
};

const capitalizeFirstWord = (input: string): string => {
    if (!input) {
        return input;
    }
    const words = input.split(" ");
    const capitalizedWords = words.map((word) => {
        if (word.length > 0) {
            return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
        }
        return word;
    });
    return capitalizedWords.join(" ");
};


const capitalizeInitials = (input: string): string => {
    const text = input.trim();
    if (!text || text.length === 0) {
        return "";
    }
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
};



const isValidString = (text: string) => {
    return text && text.length > 0;
};
const isValidEmail = (email: string) => {
    return (
        email && email.length > 0 && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
        // /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/.test(email)
    );
};

const showErrorToast = (msg: string = "something went wrong", durationInSeconds = 3) => {
    notyf.error({
        message: msg,
        duration: durationInSeconds * 1000,
    });
};

function showSuccessToast(message: string = "success") {
    notyf.success(message);
}

function padTo2Digits(num: number) {
    return num.toString().padStart(2, "0");
}

export function formatDateUI(date: any, showTime: boolean = true): string {
    if (typeof date === 'string' && (new Date(date) instanceof Date)) {
        date = new Date(date);
    }
    if (!date || !(date instanceof Date)) {
        return "NA";
    }
    let dateStr = [
        padTo2Digits(date.getDate()),
        padTo2Digits(date.getMonth() + 1),
        date.getFullYear(),
    ].join("-");
    if (showTime) {
        dateStr += ` ${padTo2Digits(date.getHours() % 12 || 12)}:${padTo2Digits(
            date.getMinutes()
        )} ${date.getHours() / 12 < 1 ? "am" : "pm"}`;
    }
    return dateStr;
}

function formatDate(date: Date | null): string {
    if (!date) {
        return "";
    }
    return [
        date.getFullYear(),
        padTo2Digits(date.getMonth() + 1),
        padTo2Digits(date.getDate()),
    ].join("-");
}

// function formatDateDDMMYYYY(date: Date | null): string {
//     if (!date) {
//         return "";
//     }
//     console.log("data->", date);
//     return [
//         padTo2Digits(date.getDate()),
//         padTo2Digits(date.getMonth() + 1),
//         date.getFullYear(),
//     ].join("-");
// }

function formatDateDDMMYYYY(date: Date | string | null): string {
    if (!date) {
        return "";
    }

    let inputDate: Date;

    // Check if the input date is a string in the format 'yyyy-mm-dd'
    if (typeof date === "string" && /^\d{4}-\d{2}-\d{2}$/.test(date)) {
        inputDate = new Date(date);
    } else if (date instanceof Date) {
        inputDate = date;
    } else {
        // Handle invalid input
        throw new Error("Invalid date format");
    }

    console.log("data->", inputDate);

    // Pad the date components to two digits
    const formattedDate = [
        padingTo2Digits(inputDate.getDate()),
        padingTo2Digits(inputDate.getMonth() + 1),
        inputDate.getFullYear(),
    ].join("-");

    return formattedDate;
}

// Helper function to pading numbers to two digits
function padingTo2Digits(num: number): string {
    return num < 10 ? "0" + num : num.toString();
}

function objectContainsEmptyValue(obj: Record<string, any>): boolean {
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            const value = obj[key];
            if (value !== "" && value !== null && value !== undefined && value.length != 0) {
                return false; // Object contains an empty value
            }
        }
    }
    return true; // Object does not contain any empty values
}

let handleImgError = (e: Event) => {
    (e.target as HTMLImageElement)?.setAttribute(
        "src",
        "/images/image-not-found/No-Image-Placeholder.png"
    );
    // console.log("h");
};


const getUrlByParamsArr = (params: string[], index: number) => {
    let url: string = "/";
    for (let i = 0; i <= index; i++) {
        const element = params[i] + "/";
        url += element;
    }
    return url;
};

const getBreadcrumbsByParamsArr = (params: string[]) => {
    let breadcrumbs: Array<{ title: string; link: string }> = [{ link: "/", title: "Home" }];
    params.map((route, i) => {
        breadcrumbs.push({
            link: getUrlByParamsArr(params, i),
            title: capitalizeFirstWord(route),
        });
    });

    return breadcrumbs;
};


export const recordGet = <K extends string, V>(record: Record<K, V>, key: K): V | undefined => {
    if (recordHas(record, key)) return record[key];
    return undefined;
};

export const recordSet = <K extends string, V>(
    record: Record<K, V>,
    key: K,
    value: V
): Record<K, V> => {
    return { ...record, [key]: value };
};

export function recordHas<K extends string, V>(record: Record<K, V>, key: K): boolean {
    return key in record;
}

export function recordDeleteKey<K extends string, V>(record: Record<K, V>, key: K): Record<K, V> {
    if (key in record) {
        delete record[key];
    }
    return { ...record };
}

export function recordMap<K extends string, V, R>(
    record: Record<K, V>,
    callback: (value: V, key: K) => R
): R[] {
    let result: R[] = [];

    for (const key in record) {
        if (record.hasOwnProperty(key)) {
            const value = record[key];
            result.push(callback(value, key));
        }
    }
    return result;
}

const getArrayFromEnum = (enumObject: any): { value: string; name: string }[] => {
    const userTypes = Object.keys(enumObject).map((key) => ({
        value: enumObject[key],
        name: key,
    }));
    return userTypes;
};

function inputFormatDate(date: Date): string {
    if (!date) return "";

    const year: number = date.getFullYear();
    const month: string = (date.getMonth() + 1).toString().padStart(2, "0"); // Months are 0-based
    const day: string = date.getDate().toString().padStart(2, "0");

    return `${year}-${month}-${day}`;
}

function sliceString(s: string, number: number): string {
    if (s.length > number) {
        return s.slice(0, number) + "..."; // Slice the string to the first 10 characters
    }
    return s; // Return the original string if it's 10 characters or fewer
}

const validatePhoneNumber = (mobileNumber: number): boolean => {
    if (!mobileNumber) return false;
    return /^[0-9]{10}$/.test(mobileNumber.toString());
};
//
export const getFileNameFromFirebaseUrl = (url: string) => {
    const regex = /\/([^\/?]+)(?=\?)/;

    // Match the URL using the regex
    const match = url.match(regex);

    if (match && match[1]) {
        // Decode the URL-encoded path and return the filename
        return decodeURIComponent(match[1]);
    }

    // Return a default filename if no match is found
    return "default_filename";
};

export const downloadStorageFile = async (url: string) => {
    try {
        const response = await fetch(url);
        const blob = await response.blob();
        const filename = getFileNameFromFirebaseUrl(url);
        downloadFileFromBlob(blob, filename);
    } catch (error) {
        console.error("Error downloading file:", error);
    }
};

const downloadFileFromBlob = (blob: Blob, filename: string) => {
    const urlObject = window.URL.createObjectURL(blob);
    const anchor = document.createElement("a");
    anchor.style.display = "none";
    anchor.href = urlObject;
    anchor.download = filename;
    document.body.appendChild(anchor);
    anchor.click();
    document.body.removeChild(anchor);
    window.URL.revokeObjectURL(urlObject);
};



export function sortByField<T>(
    array: T[],
    fieldName: keyof T,
    order: "asc" | "desc" = "asc"
): T[] {
    return array.sort((a, b) => {
        const fieldA = a[fieldName];
        const fieldB = b[fieldName];

        if (fieldA == null || fieldB == null) return 0; // Handle null/undefined values

        if (typeof fieldA === "string" && typeof fieldB === "string") {
            const comparison = fieldA.localeCompare(fieldB);
            return order === "asc" ? comparison : -comparison;
        } else if (typeof fieldA === "number" && typeof fieldB === "number") {
            return order === "asc" ? fieldA - fieldB : fieldB - fieldA;
        } else if (fieldA instanceof Date && fieldB instanceof Date) {
            const comparison = fieldA.getTime() - fieldB.getTime();
            return order === "asc" ? comparison : -comparison;
        }

        return 0; // Fallback for unsupported types
    });
}


const sentenceCaseForUI = (str: string) => {
    return str[0].toUpperCase() + str.slice(1);
}

const isNumeric = (str: string): boolean => {
    return /^\d+$/.test(str);
}

const splitCamelCase = (str: string): string => {
    return str.replace(/([a-z])([A-Z])/g, "$1 $2");
}

function separateSnakecase(input: string): string {
    return input.replace(/_/g, " ");
}

function commaSeparateNumber(text: string) {
    while (/(\d+)(\d{3})/.test(text.toString())) {
        text = text.toString().replace(/(\d+)(\d{3})/, '$1' + ',' + '$2');
    }
    return "₹ " + text;
}

function getDateDifference(startDate: Date, endDate: Date): number {
    const diffTime: number = endDate.getTime() - startDate.getTime(); // Difference in milliseconds
    console.log(diffTime);
    let diffDays = (diffTime / (1000 * 60 * 60 * 24));
    // Convert to days
    return diffDays; // Ensure negative value if endDate is before startDate
}


function getISTDateString(date:Date) {
    date.setMinutes(date.getMinutes() + date.getTimezoneOffset() + 330); // Convert UTC to IST (+5:30)
    return date.toISOString().split("T")[0]; // Format as YYYY-MM-DD
}

function getPastDate(date: Date, daysBefore: number): Date {
    const pastDate = new Date(date);
    pastDate.setDate(pastDate.getDate() - daysBefore); // Subtract the days
    return pastDate;
}

function getFutureDate(date: Date, daysAfter: number): Date {
    const futureDate = new Date(date);
    futureDate.setDate(futureDate.getDate() + daysAfter); // Add the days
    return futureDate;
}

const customSortArray = (parentArray: string[], subSetArray: string[]): string[] => {
    return subSetArray.sort((a, b) => parentArray.indexOf(a) - parentArray.indexOf(b));
}

/**
 * Safely parses JSON strings that may have been stringified multiple times.
 * This function can handle nested objects where different levels may have been
 * stringified different numbers of times.
 * 
 * @param value - The value to parse (can be string, object, array, or primitive)
 * @param maxDepth - Maximum parsing depth to prevent infinite loops (default: 10)
 * @returns The parsed value with all nested JSON strings converted to objects
 * 
 * @example
 * // Single stringification
 * const data1 = '{"name": "John", "age": 30}';
 * const parsed1 = safeJsonParse(data1); // { name: "John", age: 30 }
 * 
 * // Double stringification
 * const data2 = '"{\"name\": \"John\", \"age\": 30}"';
 * const parsed2 = safeJsonParse(data2); // { name: "John", age: 30 }
 * 
 * // Mixed stringification levels in nested objects
 * const data3 = {
 *   user: '{"name": "John"}',
 *   settings: '"{\"theme\": \"dark\"}"'
 * };
 * const parsed3 = safeJsonParse(data3); 
 * // { user: { name: "John" }, settings: { theme: "dark" } }
 */
const safeJsonParse = (value: any, maxDepth: number = 10): any => {
    // Prevent infinite recursion
    if (maxDepth <= 0) {
        return value;
    }

    // Handle null, undefined, or non-string primitives
    if (value === null || value === undefined || typeof value === 'boolean' || typeof value === 'number') {
        return value;
    }

    // Handle arrays
    if (Array.isArray(value)) {
        return value.map(item => safeJsonParse(item, maxDepth - 1));
    }

    // Handle objects (but not strings)
    if (typeof value === 'object' && typeof value !== 'string') {
        const result: any = {};
        for (const [key, val] of Object.entries(value)) {
            result[key] = safeJsonParse(val, maxDepth - 1);
        }
        return result;
    }

    // Handle strings that might be JSON
    if (typeof value === 'string') {
        // Skip empty strings
        if (value.trim() === '') {
            return value;
        }

        // Check if string looks like JSON (starts with { or [ and ends with } or ])
        const trimmed = value.trim();
        const isJsonLike = (
            (trimmed.startsWith('{') && trimmed.endsWith('}')) ||
            (trimmed.startsWith('[') && trimmed.endsWith(']')) ||
            (trimmed.startsWith('"') && trimmed.endsWith('"') && trimmed.length > 2)
        );

        if (isJsonLike) {
            try {
                const parsed = JSON.parse(value);
                // Recursively parse the result in case it's still stringified
                return safeJsonParse(parsed, maxDepth - 1);
            } catch (error) {
                // If parsing fails, return the original string
                console.warn('JSON parsing failed for value:', value.substring(0, 100) + (value.length > 100 ? '...' : ''));
                return value;
            }
        }

        // For strings that don't look like JSON, return as-is
        return value;
    }

    // Fallback for any other types
    return value;
};

/**
 * Enhanced version of safeJsonParse specifically designed for API log data.
 * This function handles common patterns in API logs where request/response bodies,
 * headers, and other nested data might be stringified multiple times.
 * 
 * @param logData - The log data object to parse
 * @returns The parsed log data with all nested JSON properly converted
 */
const parseApiLogData = (logData: any): any => {
    if (!logData || typeof logData !== 'object') {
        return safeJsonParse(logData);
    }

    const result = { ...logData };

    // Handle common API log structure
    if (result.request) {
        result.request = safeJsonParse(result.request);
        
        // Specifically handle request body and headers
        if (result.request.body) {
            result.request.body = safeJsonParse(result.request.body);
        }
        if (result.request.headers) {
            result.request.headers = safeJsonParse(result.request.headers);
        }
    }

    if (result.response) {
        result.response = safeJsonParse(result.response);
        
        // Specifically handle response body and headers
        if (result.response.body) {
            result.response.body = safeJsonParse(result.response.body);
        }
        if (result.response.headers) {
            result.response.headers = safeJsonParse(result.response.headers);
        }
    }

    // Handle error object
    if (result.error) {
        result.error = safeJsonParse(result.error);
    }

    // Parse any other top-level properties
    Object.keys(result).forEach(key => {
        if (!['request', 'response', 'error'].includes(key)) {
            result[key] = safeJsonParse(result[key]);
        }
    });

    return result;
};

export {
    customSortArray,
    getISTDateString,
    getDateDifference,
    getPastDate,
    getFutureDate,
    objectContainsEmptyValue,
    getUid,
    separateSnakecase,
    parseDate,
    debounce,
    isValidEmail,
    isValidString,
    getDateFromTimeStamp,
    capitalizeFirstWord,
    showErrorToast,
    showSuccessToast,
    formatDate,
    handleImgError,
    formatDateDDMMYYYY,
    getBreadcrumbsByParamsArr,
    validatePhoneNumber,
    formatDateToInput,
    getArrayFromEnum,
    inputFormatDate,
    sliceString,
    sentenceCaseForUI,
    isNumeric,
    commaSeparateNumber,
    splitCamelCase,
    capitalizeInitials,
    safeJsonParse,
    parseApiLogData,
};
