import { z } from "zod";
import { RAW_MATERIAL_STAUS, type IRawMaterial } from "../models/IRawMaterial";
import { CSVProvider } from "$lib/csv-provider/repositories/CSVProvider";
import { showErrorToast } from "$lib/common/utils/common-utils";
import { PresenterProvider } from "$lib/PresenterProvider";

export abstract class RawMaterialUtils {
    static getEmpty(): IRawMaterial {
        return {
            id: -1,
            name: "",
            unitId: -1,
            unitName: "",
            categoryId: -1,
            categoryName: "",
            hsn: "",
            gstPercentage: 0,
            price: 0,
            msq: 0,
            sku: "",
            status: RAW_MATERIAL_STAUS.ACTIVE,
            priceData: [],
            createdAt: new Date(),
            updatedAt: null,
            deletedAt: null,
            createdBy: '',
            updatedBy: null,
            deletedBy: null,
        }
    }


    private static rawMaterialPriceData = z.object({
        price: z.number().positive("Price must be a positive number"),
        moq: z.number().nonnegative("MOQ must be a positive number"),
        supplierId: z.number().positive("Supplier Id must be a positive number"),
    });

    static creationSchema = z.object({
        categoryId: z.number().int().positive('Category is invalid'),
        name: z.string().min(3, 'Name must be at least 3 characters long').max(100, 'Name must be up to 100 characters long'),
        unitId: z.number().int().positive('Unit is invalid'),
        msq: z.number().nonnegative('MSQ must be positive'),
        sku: z.string(),
        priceData: z.array(this.rawMaterialPriceData).min(1, 'At least one supplier is required'),
        hsn: z.string().min(3, 'HSN must be at least 3 characters long').max(50, 'HSN must be up to 50 characters long'),
        gstPercentage: z.number().positive('GST Percentage must be positive'),
    });
}

let keyMap: { [key: string]: string } = {
    'Name': 'name',
    // 'Unit Id': 'unitId',
    'Unit Name': 'unitName',
    // 'Category Id': 'categoryId',
    'Category Name': 'categoryName',
    'Sku': 'sku',
    'Msq': 'msq',
    'Hsn': 'hsn',
    'Gst Percentage': 'gstPercentage',
    // 'Price': 'price',
    'Status': 'status',
}

export const downloadCsv = async (data: IRawMaterial[], fileName: string) => {
    if (data.length === 0) {
        showErrorToast("Please choose data")
        return;
    }
    await new CSVProvider().save(data, fileName, keyMap);
}

export const downloadAllCsv = async (startDate: Date, endDate: Date) => {
    const response = await PresenterProvider.rawMaterialPresenter.getAll(1, 2, startDate, endDate)
    if (response.success && response.data.data && response.data.data.length > 0) {
        await new CSVProvider().save(response.data.data, "raw-materials", keyMap);
    } else {
        showErrorToast("No data found")
    }
}