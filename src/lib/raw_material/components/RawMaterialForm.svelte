<script lang="ts">
    import { goto } from "$app/navigation";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import {
        capitalizeFirstWord,
        showErrorToast,
        showSuccessToast,
    } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { Label, Input, Select, CloseButton } from "flowbite-svelte";

    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import { RAW_MATERIAL_STAUS, type IRawMaterial } from "../models/IRawMaterial";
    import { RawMaterialUtils } from "../utils/RawMaterialUtils";
    import { onMount } from "svelte";
    import type { IItemUnit } from "$lib/item_unit/models/IItemUnit";
    import type { IItemCategory } from "$lib/item_category/models/IItemCategory";
    import SupplierSearch from "$lib/supplier/components/SupplierSearch.svelte";
    import type { ISupplier } from "$lib/supplier/models/ISupplier";
    import type { IdTitle } from "$lib/IdTitle/models/IdTitle";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import ItemUnitDropdown from "$lib/item_unit/components/ItemUnitDropdown.svelte";
    import ItemCategorySearch from "$lib/item_category/components/ItemCategorySearch.svelte";

    export let obj: IRawMaterial = RawMaterialUtils.getEmpty();
    export let isInsideModal: boolean = false;
    export let onSubmitSuccess: null | ((data: IRawMaterial) => void) = null;

    let selectedCategory: IdTitle | null = null;
    let isDoingTask: boolean = false;
    let isLoadingUnits: boolean = true;
    let isLoadingCategories: boolean = true;

    let validationErrors: Map<string, string> = new Map();

    let itemUnits: IItemUnit[] = [];
    let itemCategories: IItemCategory[] = [];

    export let suppliers: {
        supplier: IdTitle;
        price: number;
        moq: number;
    }[] = [];

    let showAssignSuppliers: boolean = false;

    const handleMsq = (e: any) => {
        try {
            if (e.target.value === "") {
                obj.msq = 0;
                return;
            }

            obj.msq = parseFloat(e.target.value);
        } catch (error) {
            obj.msq = 0;
        }
    };
    const handleGstPercentage = (e: any) => {
        try {
            if (e.target.value === "") {
                obj.gstPercentage = 0;
                return;
            }

            obj.gstPercentage = parseFloat(e.target.value);
        } catch (error) {
            obj.gstPercentage = 0;
        }
    };

    const handlePrice = (e: any, index: number) => {
        try {
            if (e.target.value === "") {
                suppliers[index].price = 0;
                return;
            }

            suppliers[index].price = parseFloat(e.target.value);
        } catch (error) {
            suppliers[index].price = 0;
        }
    };

    const handleMOQ = (e: any, index: number) => {
        try {
            if (e.target.value === "") {
                suppliers[index].moq = 0;
                return;
            }

            suppliers[index].moq = parseFloat(e.target.value);
        } catch (error) {
            suppliers[index].moq = 0;
        }
    };

    const assignSupplier = (supplier: ISupplier) => {
        if (suppliers.some((item) => item.supplier.id === supplier.id)) {
            return showErrorToast("Supplier already assigned");
        }
        suppliers.push({
            supplier: {
                id: supplier.id,
                title: supplier.name,
            },
            price: 0,
            moq: 0,
        });

        suppliers = suppliers;
    };

    const handleSubmit = async () => {
        if (suppliers.length > 0) {
            obj.priceData = suppliers.map((item) => ({
                price: item.price,
                moq: item.moq,
                supplierId: item.supplier.id,
                rawMaterial: "",
                rawMaterialId: obj.id,
                supplier: item.supplier.title,
            }));
        } else {
            obj.priceData = [];
        }
        validationErrors = PresenterProvider.rawMaterialPresenter.onValidate(obj);

        if (validationErrors.size !== 0) {
            showErrorToast(validationErrors.get(validationErrors.keys().next().value!));
            return;
        }
        else if(obj.priceData.length === 0){
            showErrorToast("Please add at least one price data");
            return;
        }
        isDoingTask = true;

        let res;
        if (obj.id > 0) {
            res = await PresenterProvider.rawMaterialPresenter.onUpdate(obj.id, obj);
        } else {
            res = await PresenterProvider.rawMaterialPresenter.onSubmit(obj);
        }

        if (res.success) {
            showSuccessToast(`Done`);
            if (onSubmitSuccess) {
                onSubmitSuccess(res.data! as IRawMaterial);
            } else {
                await goto("/admin/raw-materials");
            }
        } else {
            showErrorToast(res.message);
            isDoingTask = false;
        }
    };
    const _loadUnits = async () => {
        const response = await PresenterProvider.rawMaterialPresenter.getItemUnits(1, 1000);
        if (!response.success) {
            showErrorToast(response.message);
        } else {
            itemUnits = response.data.data;
            if (itemUnits.length === 0) {
                showErrorToast("Please add at least one unit to add a raw material", 6);

                return goto("/admin/item-units/add");
            }
            if (obj.id <= 0) {
                const id = itemUnits[0].id;
                obj.unitId = id;
            }
            obj.unitId = obj.unitId;
            isLoadingUnits = false;
        }
    };

    const _loadCategories = async () => {
        const response = await PresenterProvider.rawMaterialPresenter.getItemCategories(1, 1000);
        if (!response.success) {
            showErrorToast(response.message);
        } else {
            itemCategories = response.data.data;
            if (itemCategories.length === 0) {
                showErrorToast("Please add at least one category to add a raw material", 6);

                return goto("/admin/item-categories/add");
            }

            if (obj.id <= 0) {
                obj.categoryId = itemCategories[0].id;
            }
            obj.categoryId = obj.categoryId;
            isLoadingCategories = false;
        }
    };

    onMount(() => {
        _loadUnits();
        // _loadCategories();

        if (obj.id > 0) {
            selectedCategory = {
                id: obj.categoryId,
                title: obj.categoryName,
            };
            obj.categoryId = selectedCategory.id;
        }

        for (const item of obj.priceData) {
            suppliers.push({
                supplier: {
                    id: item.supplierId,
                    title: item.supplier,
                },
                price: item.price,
                moq: item.moq,
            });
        }
        suppliers = suppliers;

        // showAssignSuppliers = suppliers.length > 0;
        showAssignSuppliers = true;
    });
</script>

{#if isLoadingUnits}
    <PageLoader />
{:else}
    <div class="flex items-center justify-center">
        <div class=" w-[90vw] p-2">
            {#if !isInsideModal}
                <div class=" flex items-center justify-between py-2">
                    <FormHeader label={obj.id > 0 ? "Edit raw material" : "Add raw material"}
                    ></FormHeader>
                    <BreadCrumbs breadCrumbData={[]} />
                </div>
                <hr class="mb-5" />
            {/if}

            <div class=" grid grid-cols-2 gap-6">
                <div>
                    <ItemCategorySearch
                        onSelected={(data) => {
                            if (data) {
                                obj.categoryId = data.id;
                                selectedCategory = {
                                    id: data.id,
                                    title: data.name,
                                };
                            } else {
                                obj.categoryId = -1;
                                selectedCategory = null;
                            }
                        }}
                        selected={selectedCategory}
                    />
                    {#if validationErrors.has("categoryId")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("categoryId")}
                        </p>
                    {/if}
                </div>
            </div>

            <div class="m-2"></div>

            <div class=" grid grid-cols-2 gap-6">
                <div>
                    <Label for="name" class="mb-2 font-sans capitalize tracking-[0px]">
                        Name
                        {#if validationErrors.has("name")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Input
                        type="text"
                        id="name"
                        placeholder="Name"
                        class="dark:bg-primary-700 {validationErrors.has('name')
                            ? 'border-red-500'
                            : ''}"
                        bind:value={obj.name}
                    />
                    {#if validationErrors.has("name")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("name")}
                        </p>
                    {/if}
                </div>

                <div>
                    <Label for="unit" class="mb-2 font-sans capitalize tracking-[0px]">
                        Unit
                        {#if validationErrors.has("name")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>

                    <ItemUnitDropdown
                        data={itemUnits}
                        id="unit"
                        cssClass="dark:bg-primary-700 {validationErrors.has('unit')
                            ? 'border-red-500'
                            : ''}"
                        selectedValue={obj.unitId}
                        onSelected={(data) => {
                            obj.unitId = Number(data);
                        }}
                    />

                    {#if validationErrors.has("unit")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("unit")}
                        </p>
                    {/if}
                </div>
            </div>
            <div class="m-2"></div>

            <div class=" grid grid-cols-2 gap-6">
                <div>
                    <Label for="sku" class="mb-2 font-sans capitalize tracking-[0px]">
                        SKU
                        {#if validationErrors.has("sku")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Input
                        type="text"
                        id="sku"
                        placeholder="SKU"
                        class="uppercase dark:bg-primary-700 {validationErrors.has('sku')
                            ? 'border-red-500'
                            : ''}"
                        bind:value={obj.sku}
                    />
                    {#if validationErrors.has("sku")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("sku")}
                        </p>
                    {/if}
                </div>

                <div>
                    <Label for="msq" class="mb-2 font-sans capitalize tracking-[0px]">
                        MSQ
                        {#if validationErrors.has("msq")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Input
                        type="text"
                        id="msq"
                        placeholder="MSQ"
                        class="dark:bg-primary-700 {validationErrors.has('msq')
                            ? 'border-red-500'
                            : ''}"
                        value={obj.msq}
                        on:change={handleMsq}
                    />
                    {#if validationErrors.has("msq")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("msq")}
                        </p>
                    {/if}
                </div>
            </div>
            <div class="m-2"></div>

            <div class=" grid grid-cols-2 gap-6">
                <div>
                    <Label for="hsn" class="mb-2 font-sans capitalize tracking-[0px]">
                        HSN
                        {#if validationErrors.has("hsn")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Input
                        type="text"
                        id="hsn"
                        placeholder="HSN"
                        class="uppercase   dark:bg-primary-700 {validationErrors.has('hsn')
                            ? 'border-red-500'
                            : ''}"
                        bind:value={obj.hsn}
                    />
                    {#if validationErrors.has("hsn")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("hsn")}
                        </p>
                    {/if}
                </div>

                <div>
                    <Label for="gstPercentage" class="mb-2 font-sans capitalize tracking-[0px]">
                        GST Percentage
                        {#if validationErrors.has("gstPercentage")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Input
                        type="text"
                        id="gstPercentage"
                        placeholder="GST Percentage"
                        class="dark:bg-primary-700 {validationErrors.has('gstPercentage')
                            ? 'border-red-500'
                            : ''}"
                        value={obj.gstPercentage}
                        on:change={handleGstPercentage}
                    />
                    {#if validationErrors.has("gstPercentage")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("gstPercentage")}
                        </p>
                    {/if}
                </div>
            </div>
            <div class="m-2"></div>

            <div class=" grid grid-cols-2 gap-6">
                {#if obj.id > 0}
                    <div>
                        <Label for="status" class="mb-2 font-sans capitalize tracking-[0px]">
                            Status
                            {#if validationErrors.has("status")}
                                <span class="text-red-600">*</span>
                            {/if}
                        </Label>
                        <Select
                            id="status"
                            class="dark:bg-primary-700 {validationErrors.has('status')
                                ? 'border-red-500'
                                : ''}"
                            items={Object.values(RAW_MATERIAL_STAUS).map((item) => ({
                                value: item,
                                name: item.toUpperCase(),
                            }))}
                            bind:value={obj.status}
                        />
                        {#if validationErrors.has("status")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("status")}
                            </p>
                        {/if}
                    </div>
                {/if}
            </div>
            <div class="m-2"></div>

            {#if obj.id > 0 && suppliers.length > 0}
                <div class="font-bold text-xl">Assigned Suppliers</div>
            {:else}
                <div class="font-bold text-xl">Assign Suppliers</div>
                <!-- <CustomSwitch
                    label="Assign a supplier"
                    checked={showAssignSuppliers}
                    onChange={(value) => {
                        showAssignSuppliers = value;
                        if (showAssignSuppliers === false) {
                            suppliers = [];
                        }
                    }} 
                /> -->
            {/if}

            {#if showAssignSuppliers}
                <div class="m-2"></div>
                <SupplierSearch onSelected={assignSupplier} />
                <div class="m-2"></div>
                <div class="mt-4"></div>

                <table
                    class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right border"
                >
                    <thead
                        class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
                    >
                        <tr>
                            <th scope="col" class="px-6 py-3">SR No.</th>
                            <th scope="col" class="px-6 py-3">Supplier</th>
                            <th scope="col" class="px-6 py-3">Price</th>
                            <th scope="col" class="px-6 py-3">MOQ</th>
                            <th scope="col" class="px-6 py-3">Remove</th>
                        </tr>
                    </thead>
                    <tbody>
                        {#each suppliers as item, index}
                            <tr
                                class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                            >
                                <td class="px-6 py-4">
                                    {index + 1}
                                </td>
                                <td class="px-6 py-4">
                                    {capitalizeFirstWord(item.supplier.title)}
                                </td>

                                <td class="px-6 py-4">
                                    <Input
                                        placeholder={"Price"}
                                        value={item.price}
                                        on:change={(e) => {
                                            handlePrice(e, index);
                                        }}
                                    />
                                </td>
                                <td class="px-6 py-4">
                                    <Input
                                        placeholder={"MOQ"}
                                        value={item.moq}
                                        on:change={(e) => {
                                            handleMOQ(e, index);
                                        }}
                                    />
                                </td>
                                <td class="px-6 py-4">
                                    <CloseButton
                                        class="w-content"
                                        on:click={() => {
                                            suppliers = suppliers.filter(
                                                (obj) => obj.supplier.id !== item.supplier.id
                                            );
                                        }}
                                    />
                                </td>
                            </tr>
                        {/each}
                    </tbody>
                </table>

                <div class="m-2"></div>
            {/if}

            <div class="m-2"></div>
            <div class="mt-5 flex w-full justify-end">
                <CustomButton
                    onClick={handleSubmit}
                    cssClass="w-32 bg-black"
                    title={"Save"}
                    isLoading={isDoingTask}
                />
            </div>
        </div>
    </div>
{/if}
