import type { IRawMaterial } from "$lib/raw_material/models/IRawMaterial";
import type { ISupplier } from "$lib/supplier/models/ISupplier";
import type { IDepartment } from "$lib/department/models/IDepartment";

export interface IPurchaseOrderItems {
    id: number;
    qty: number;
    name: string;
}

export interface PurchaseItemDetails{
  item:IRawMaterial,
  qty:number;
}

export interface IPurchaseOrder
{
    id:number;
    poNumber:string;
    supplier: ISupplier;
    expectedDate:Date;
    items: PurchaseItemDetails[];
    createdByName:string|undefined;
    createdAt:Date|undefined;
    fromDepartment: IDepartment | null;
    toDepartment: IDepartment | null;
    supplierContactPerson?: string;
}
export interface IPurchaseOrderPayload extends IPurchaseOrder {
    orderItems:IPurchaseOrderItems[];
}

export interface ICreatePurchaseOrderPayload
{
  poNumber:string,
  supplierId:number,
  expectedDate:Date,
  items: IPurchaseOrderItems[],
  fromDepartmentId: number,
  toDepartmentId: number,
  supplierContactPerson?: string,
}

export interface IUpdatePurchaseOrderPayload extends ICreatePurchaseOrderPayload
{
  id:number,
}
