<script lang="ts">
    import { Spinner } from "flowbite-svelte";
    import {
        capitalizeFirstWord,
        commaSeparateNumber,
        debounce,
        formatDateUI,
    } from "$lib/common/utils/common-utils";
    import { onMount } from "svelte";
    import Loader from "$lib/common/components/Loader.svelte";
    import type { IDebitNote } from "../models/IDebitNote";
    import { loggedInUser } from "$lib/common/utils/store";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import { jsPDF } from "jspdf";
    import { userPermissions } from "$lib/common/utils/store";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";

    export let totalRows: IDebitNote[] = [];
    export let searchTerm: string = "";

    let isLoading: boolean = true;
    let searchLoading: boolean = false;
    let filteredData: IDebitNote[] = [];
    let totalAmount = 0;
    let totalQty = 0;

    let debounceSearch = debounce(() => {
        filteredData = totalRows.filter((data) =>
            data.supplier.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }, 300);

    const printDebitNote = () => {
        isLoading = true;
        const printElement = document.getElementById("print-debit-note");
        if (printElement) {
            printElement.style.visibility = "visible";
            const doc = new jsPDF("p", "pt", "a4");
            doc.html(printElement, {
                callback: function (doc) {
                    doc.save(
                        "Debit note for invoice " +
                            totalRows[0].purchaseInvoice.toUpperCase() +
                            ".pdf"
                    );

                    setTimeout(() => {
                        printElement.style.visibility = "hidden";
                        isLoading = false;
                    }, 600);
                },
            });
        }
    };

    onMount(() => {
        filteredData = totalRows;
        totalAmount = Number(totalRows.reduce((acc, curr) => acc + curr.debitAmount, 0).toFixed(2));
        // totalQty = Number(totalRows.reduce((acc, curr) => acc + curr.qty, 0).toFixed(2));
        isLoading = false;
    });
</script>

{#if isLoading}
    <div class="flex h-[80vh] w-[100%] items-center justify-center">
        <Spinner size={"10"} color="gray" />
    </div>
{:else}
    <h1
        class="mb-4 font-primary text-2xl font-medium capitalize tracking-[0px] text-black dark:text-gray-100"
    >
        Debit Notes
    </h1>

    <div class="relative overflow-x-auto font-primary shadow-md sm:rounded-lg">
        <div
            class="flex-column flex flex-wrap items-center justify-between space-y-4 rounded-lg bg-[#E3E3E3] p-4 text-sm focus:ring-primary-500 dark:bg-[#131313] md:flex-row md:space-y-0"
        >
            <label for="table-search" class="sr-only">Search</label>

            <div class="relative">
                <div
                    class="rtl:inset-r-0 pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3"
                >
                    <img src="/images/svg/search.svg" alt="l" width="15px" />
                </div>
                <input
                    type="text"
                    id="franchise-search"
                    bind:value={searchTerm}
                    on:input={debounceSearch}
                    class="block w-96 rounded-lg p-2 ps-10 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:bg-primary-800 dark:text-white"
                    placeholder="Search by name"
                />
            </div>
            <div class="space-x-4"></div>
        </div>

        {#if searchLoading}
            <div class="flex h-[30rem] w-full items-center justify-center">
                <Loader />
            </div>
        {:else}
            <table class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right">
                <thead
                    class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
                >
                    <tr>
                        <th scope="col" class="px-6 py-3">SR No.</th>
                        <th scope="col" class="px-6 py-3">Supplier name</th>
                        <th scope="col" class="px-6 py-3">Invoice number</th>
                        <th scope="col" class="px-6 py-3">Invoice date</th>
                        <th scope="col" class="px-6 py-3">Raw material name</th>
                        <th scope="col" class="px-6 py-3">Qty</th>
                        <th scope="col" class="px-6 py-3">Agreed price</th>
                        <th scope="col" class="px-6 py-3">Purchased price</th>
                        <th scope="col" class="px-6 py-3">Source</th>
                        <th scope="col" class="px-6 py-3">Debit amount</th>
                    </tr>
                </thead>
                <tbody>
                    {#each filteredData as row, index}
                        <tr
                            class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                        >
                            <td class="px-6 py-4">
                                <a
                                    href={"#"}
                                    class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                    {index + 1}
                                </a>
                            </td>
                            <td class="px-6 py-4">
                                <a
                                    href={"#"}
                                    class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                    {capitalizeFirstWord(row.supplier)}
                                </a>
                            </td>
                            <td class="px-6 py-4">
                                <a
                                    href={"#"}
                                    class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                    {row.purchaseInvoice}
                                </a>
                            </td>
                            <td class="px-6 py-4">
                                <a
                                    href={"#"}
                                    class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                    {formatDateUI(row.purchaseInvoiceDate, false)}
                                </a>
                            </td>
                            <td class="px-6 py-4">
                                <a
                                    href={"#"}
                                    class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                    {capitalizeFirstWord(row.rawMaterial)}
                                </a>
                            </td>
                            <td class="px-6 py-4">
                                <a
                                    href={"#"}
                                    class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                    {row.qty}
                                </a>
                            </td>
                            <td class="px-6 py-4">
                                <a
                                    href={"#"}
                                    class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                    ₹ {row.actualPrice}
                                </a>
                            </td>
                            <td class="px-6 py-4">
                                <a
                                    href={"#"}
                                    class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                    ₹ {row.purchasedPrice}
                                </a>
                            </td>
                            <td class="px-6 py-4">
                                <a
                                    href={"#"}
                                    class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                    {row.source.toUpperCase()}
                                </a>
                            </td>
                            <td class="px-6 py-4">
                                <a
                                    href={"#"}
                                    class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                    ₹ {row.debitAmount}
                                </a>
                            </td>
                        </tr>
                    {/each}
                    {#if filteredData.length === 0}
                        <tr class="font-medium text-black dark:text-gray-400">
                            <td colspan="6" class="h-[50vh] text-center">Yet no debit notes</td>
                        </tr>
                    {/if}
                </tbody>
            </table>
        {/if}
    </div>
    {#if $userPermissions.includes(AppPermissions.DEBIT_NOTE.PRINT)}
        <div class="mt-3">
            <CustomButton title="Print" onClick={printDebitNote} />
        </div>
    {/if}
{/if}

<div
    id="print-debit-note"
    style="
    width:450pt;
    max-width: 450pt;
        padding: 20px;
        font-size: 8px;"
>
    <div style="margin-bottom: 20px;;">
        <img
            src="/images/logo.png"
            alt="logo"
            style="width:100px;height:70px;margin-left:auto;margin-right:auto;"
        />
    </div>
    <div class="w-full mb-[2rem] text-center">
        <h1 class="text-[1rem] font-bold">Debit Note #{totalRows[0].purchaseInvoiceId}</h1>
    </div>

    <div
        style=" display: flex;
        justify-content: space-between;
        margin-bottom: 10px;"
    >
        <div style="display: inline-block;">
            <span style=" font-weight: bold;margin-right: 5px;">Invoice No.:</span>
            {totalRows[0].purchaseInvoice.toUpperCase()}
        </div>
        <div style="display: inline-block;">
            <span style=" font-weight: bold;margin-right: 5px;">Date:</span>
            {formatDateUI(new Date())}
        </div>
    </div>
    <div style="margin-bottom: 5px;">
        <span style=" font-weight: bold;margin-right: 5px;">Supplier:</span>
        {totalRows[0].supplier.toUpperCase()}
    </div>
    <div style="margin-bottom: 5px;">
        <span style=" font-weight: bold;margin-right: 5px;">Total Amount:</span>
        {totalAmount}
    </div>
    <div style="margin-bottom: 5px;">
        <span style=" font-weight: bold;margin-right: 5px;">Created By:</span>
        {$loggedInUser?.firstName.toUpperCase()}
    </div>
    <div
        style=" margin-top: 20px;
        width: 100%;"
    >
        <table
            id="print-table"
            style=" 
            font-size: 8px;
            width: 100%;
        border-collapse: collapse;
        border: 1px solid #ddd;"
        >
            <thead>
                <tr>
                    <th
                        class="whitespace-nowrap"
                        style="
border: 1px solid #ddd;
        padding: 4px;
        text-align: left;
        vertical-align: middle;
         background-color: #f2f2f2;
        font-weight: bold;"
                    >
                        Sr. No.
                    </th>
                    <th
                        class="whitespace-nowrap"
                        style="
border: 1px solid #ddd;
        padding: 4px;
        text-align: left;
        vertical-align: middle;
         background-color: #f2f2f2;
        font-weight: bold;"
                    >
                        Item
                    </th>
                    <th
                        class="whitespace-nowrap"
                        style="
border: 1px solid #ddd;
        paddingrejectionReason     text-align: left;
        vertical-align: middle;
         background-color: #f2f2f2;
        font-weight: bold;"
                    >
                        Qty
                    </th>
                    <th
                        class="whitespace-nowrap"
                        style="
border: 1px solid #ddd;
        padding: 4px;
        text-align: left;
        vertical-align: middle;
         background-color: #f2f2f2;
        font-weight: bold;"
                    >
                        Agreed Price (Unit)
                    </th>
                    <th
                        class="whitespace-nowrap"
                        style="
border: 1px solid #ddd;
        padding: 4px;
        text-align: left;
        vertical-align: middle;
         background-color: #f2f2f2;
        font-weight: bold;"
                    >
                        Purchased Price(Unit)
                    </th>
                    <th
                        class="whitespace-nowrap"
                        style="
border: 1px solid #ddd;
        padding: 4px;
        text-align: left;
        vertical-align: middle;
         background-color: #f2f2f2;
        font-weight: bold;"
                    >
                        Amount
                    </th>
                    <th
                        class="whitespace-nowrap"
                        style="
border: 1px solid #ddd;
        padding: 4px;
        text-align: left;
        vertical-align: middle;
         background-color: #f2f2f2;
        font-weight: bold;"
                    >
                        Reason (if any)
                    </th>
                </tr>
            </thead>
            <tbody>
                {#each totalRows as row, index}
                    <tr>
                        <td
                            style="border: 1px solid #ddd; padding: 4px; text-align: left; vertical-align: middle;"
                        >
                            {index + 1}
                        </td>
                        <td
                            style="border: 1px solid #ddd; padding: 4px; text-align: left; vertical-align: middle;"
                        >
                            {row.rawMaterial.toUpperCase()}
                        </td>
                        <td
                            style="border: 1px solid #ddd; padding: 4px; text-align: left; vertical-align: middle;"
                        >
                            {row.qty}
                        </td>
                        <td
                            style="border: 1px solid #ddd; padding: 4px; text-align: left; vertical-align: middle;"
                        >
                            {row.actualPrice}
                        </td>
                        <td
                            style="border: 1px solid #ddd; padding: 4px; text-align: left; vertical-align: middle;"
                        >
                            {row.purchasedPrice}
                        </td>
                        <td
                            style="border: 1px solid #ddd; padding: 4px; text-align: left; vertical-align: middle;"
                        >
                            {row.debitAmount}
                        </td>
                        <td
                            style="border: 1px solid #ddd; padding: 4px; text-align: left; vertical-align: middle;"
                        >
                            {row.rejectionReason}
                        </td>
                    </tr>
                {/each}

                <!-- Calculate Total Quantity & Total Amount -->
                {#if totalRows.length > 0}
                    <tr style="font-weight: bold; background-color: #f1f1f1;">
                        <td
                            colspan="2"
                            style="border: 1px solid #ddd; padding: 4px; text-align: right;"
                        >
                            Total:
                        </td>
                        <td style="border: 1px solid #ddd; padding: 4px; text-align: left;">
                            {Number(totalRows.reduce((sum, row) => sum + Number(row.qty), 0)).toFixed(2)}
                        </td>
                        <td colspan="2" style="border: 1px solid #ddd;"></td>
                        <td style="border: 1px solid #ddd; padding: 4px; text-align: left;">
                            {Number(
                                totalRows.reduce((sum, row) => sum + row.debitAmount, 0)
                            ).toFixed(2)}
                        </td>

                        <td style="border: 1px solid #ddd;"></td>
                    </tr>
                {/if}
            </tbody>
        </table>

        <div style="margin-top: 20px;">Authorized Signature __________________</div>
    </div>
</div>

<style>
    #print-debit-note {
        visibility: hidden;
    }
</style>
