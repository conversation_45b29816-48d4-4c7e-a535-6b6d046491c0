<script lang="ts">
    import CustomModal from "$lib/common/components/CustomModal.svelte";
    import SearchWithDrop from "$lib/common/components/SearchWithDrop.svelte";
    import { debounce } from "$lib/common/utils/common-utils";
    import { RepoProvider } from "$lib/RepoProvider";
    import type { ISupplier } from "../models/ISupplier";
    import { SupplierUtils } from "../utils/SupplierUtils";
    import SupplierForm from "./SupplierForm.svelte";

    export let isLabel: boolean = true;
    export let selected: ISupplier | null = null;
    export let onSelected: (data: ISupplier) => void;
    export let disabled: boolean = false;

    let searchTerm: string = "";
    let fetchedData: ISupplier[] = [];
    let showAddNewModal: boolean = false;

    const getData = async () => {
        const res = await RepoProvider.supplierRepo.search(searchTerm);
        if (res.success) {
            fetchedData = res.data;
        }
        if (fetchedData.length === 0) {
            const obj = SupplierUtils.getEmpty();
            obj.name = "Add new";
            fetchedData = [obj];
        }
    };

    const debounceSearch = debounce(getData, 600);

    const doSearch = (search: string) => {
        searchTerm = search; // Update the search term
        debounceSearch(); // Call the debounced search
    };

    const addNew = () => {
        showAddNewModal = true;
    };
</script>

<SearchWithDrop
    {isLabel}
    {disabled}
    label="Suppliers"
    bind:searchTerm
    level="name"
    searchInput={doSearch}
    selected={selected?.name ?? null}
    selectedObj={selected}
    selectedFunc={(data) => {
        fetchedData = [];
        searchTerm = "";
        if (data) {
            if (data.id === -1) {
                return addNew();
            }
        }
        onSelected(data);
    }}
    bind:filteredData={fetchedData}
/>

<CustomModal title="Add New Supplier" bind:showModal={showAddNewModal}>
    <SupplierForm
        isInsideModal={true}
        onSubmitSuccess={(data) => {
            fetchedData = [];
            searchTerm = "";
            onSelected(data);
            showAddNewModal = false;
        }}
    />
</CustomModal>
