<script lang="ts">
    import { goto } from "$app/navigation";
    import CustomButton from "$lib/common/components/admin/CustomButton.svelte";
    import {
        formatDateToInput,
        showErrorToast,
        showSuccessToast,
    } from "$lib/common/utils/common-utils";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import { Label, Input, CloseButton } from "flowbite-svelte";

    import FormHeader from "$lib/common/components/admin/FormHeader.svelte";
    import BreadCrumbs from "$lib/common/components/admin/BreadCrumbs.svelte";
    import { onMount } from "svelte";

    import type { ISupplier } from "$lib/supplier/models/ISupplier";
    import type { IStorageLocation } from "$lib/storage_locations/models/IStorageLocation";
    import type { IFactoryGate } from "$lib/factory_gates/models/IFactoryGate";
    import SupplierSearch from "$lib/supplier/components/SupplierSearch.svelte";
    import { PurchaseInvoiceUtils } from "../utils/PurchaseInvoiceUtils";
    import SupplierRawMaterialSearch from "./SupplierRawMaterialSearch.svelte";
    import type { IRawMaterial } from "$lib/raw_material/models/IRawMaterial";
    import type { IUser } from "$lib/users/models/User";
    import PageLoader from "$lib/common/components/PageLoader.svelte";
    import type {
        IPurchaseInvoiceReceivedItem,
        IPurchaseInvoiceReceivedItemDetails,
        IPurchaseInvoiceReceivedItemInfo,
        IRejectedItemDetails,
    } from "../models/IPurchaseInvoice";
    import FactoryGateDropdown from "$lib/factory_gates/components/FactoryGateDropdown.svelte";
    import UsersDropdown from "$lib/users/components/admin/UsersDropdown.svelte";
    import { AppPermissions } from "$lib/users/sub-features/permissions/models/AppPermissions";
    import { userPermissions } from "$lib/common/utils/store";
    import ManualDebitNotes from "./ManualDebitNotes.svelte";
    import PoSearch from "./POSearch.svelte";
    import type { IPurchaseOrder } from "$lib/purchase-order/models/IPurchaseOrder";
    import { SupplierUtils } from "$lib/supplier/utils/SupplierUtils";

    export let obj = PurchaseInvoiceUtils.getEmpty();
    export let isUserHasEditPermission = false;
    let isDoingTask: boolean = false;
    let isLoadingFactoryGates: boolean = true;
    let isLoadingStorageLocations: boolean = true;
    let isLoadingUsers: boolean = true;
    let isFullEdit: boolean = false;

    let validationErrors: Map<string, string> = new Map();
    let selectedSupplier: ISupplier | null = null;

    let factoryGates: IFactoryGate[] = [];
    let storageLocations: IStorageLocation[] = [];
    let users: IUser[] = [];

    let oldRawMaterialsIds: number[] = [];

    const handleTotalQty = (e: any, index: number) => {
        try {
            if(selectedPO && obj.rawMaterials[index].totalQty && obj.rawMaterials[index].totalQty > 0){
                let input = e.target.value;
                if(input > obj.rawMaterials[index].totalQty){
                    e.target.value = obj.rawMaterials[index].totalQty;
                    showErrorToast("Please enter excess quantity in Excess Field!");
                    return;
                }
            }else{
                obj.rawMaterials[index].totalQty = parseFloat(e.target.value);
            }
        } catch (error) {
            obj.rawMaterials[index].totalQty = 0;
        }
    };

    const handleRejectedQty = (e: any, index: number) => {
        try {
            if (e.target.value === "") {
                obj.rawMaterials[index].rejectedQty = 0;
                return;
            }

            obj.rawMaterials[index].rejectedQty = parseFloat(e.target.value);
        } catch (error) {
            obj.rawMaterials[index].rejectedQty = 0;
        }
    };

    const handleHoldQty = (e: any, index: number) => {
        try {
            if (e.target.value === "") {
                obj.rawMaterials[index].holdQty = 0;
                return;
            }

            obj.rawMaterials[index].holdQty = parseFloat(e.target.value);
        } catch (error) {
            obj.rawMaterials[index].holdQty = 0;
        }
    };
    const handleExcessQty = (e: any, index: number) => {
        try {
            if (e.target.value === "" || e.target.value < 0) {
                obj.rawMaterials[index].excessQty = 0;
                e.target.value = 0;
                return;
            }

            obj.rawMaterials[index].excessQty = parseFloat(e.target.value);
        } catch (error) {
            obj.rawMaterials[index].excessQty = 0;
        }
    };
    const handleReplacedQty = (e: any, index: number) => {
        try {
            if (e.target.value === "") {
                obj.rawMaterials[index].replaceableQty = 0;
                e.target.value = 0;
                return;
            }

            obj.rawMaterials[index].replaceableQty = parseFloat(e.target.value);
        } catch (error) {
            obj.rawMaterials[index].replaceableQty = 0;
        }
    };

    const handlePrice = (e: any, index: number) => {
        try {
            if (e.target.value === "") {
                obj.rawMaterials[index].price = 0;
                return;
            }

            obj.rawMaterials[index].price = parseFloat(e.target.value);
        } catch (error) {
            obj.rawMaterials[index].price = 0;
        }
    };

    const handleSubmit = async () => {
        console.log("obj bef validation-->",obj)
        validationErrors = PresenterProvider.purchaseInvoicePresenter.onValidate(obj);
        console.log(validationErrors);
        
        if (validationErrors.size !== 0) {
            showErrorToast(validationErrors.get(validationErrors.keys().next().value!));
            return;
        }
        isDoingTask = true;
        let res;

        let allRawMaterials = obj.rawMaterials.map((item) => item);

        if (obj.id > 0) {
            if (!isFullEdit) {
                const newItems = obj.rawMaterials.filter(
                    (item) => !oldRawMaterialsIds.includes(item.rawMaterialId)
                );

                if (newItems.length === 0) {
                    isDoingTask = false;
                    showErrorToast("Please add at least one new item");
                    return;
                }
                obj.rawMaterials = newItems;
            }

            if (!isFullEdit) {
                res = await PresenterProvider.purchaseInvoicePresenter.onUpdate({
                    id: obj.id,
                    factoryGateId: obj.factoryGateId,
                    invoiceDate: obj.invoiceDate,
                    invoiceNumber: obj.invoiceNumber,
                    poDate: obj.poDate,
                    poNumber: obj.poNumber,
                    purchasedById: obj.purchasedById,
                    rawMaterials: obj.rawMaterials,
                    supplierId: obj.supplierId,
                    purchaseOrderId: null,
                    // purchaseOrderId: selectedPO?.id ?? null,
                });
            } else {
                res = await PresenterProvider.purchaseInvoicePresenter.onUpdateFull({
                    id: obj.id,
                    factoryGateId: obj.factoryGateId,
                    invoiceDate: obj.invoiceDate,
                    invoiceNumber: obj.invoiceNumber,
                    poDate: obj.poDate,
                    poNumber: obj.poNumber,
                    purchasedById: obj.purchasedById,
                    rawMaterials: obj.rawMaterials,
                    supplierId: obj.supplierId,
                    purchaseOrderId: null,
                    // purchaseOrderId: selectedPO?.id ?? null,
                });
            }
        } else {
            res = await PresenterProvider.purchaseInvoicePresenter.onSubmit({
                factoryGateId: obj.factoryGateId,
                invoiceDate: obj.invoiceDate,
                invoiceNumber: obj.invoiceNumber,
                poDate: obj.poDate,
                poNumber: obj.poNumber,
                purchasedById: obj.purchasedById,
                rawMaterials: obj.rawMaterials,
                supplierId: obj.supplierId,
                purchaseOrderId: selectedPO?.id ?? null,
            });
        }

        if (res.success) {
            showSuccessToast(`Done`);
            await goto("/admin/purchase-invoices");
        } else {
            obj.rawMaterials = allRawMaterials;
            showErrorToast(res.message);
            isDoingTask = false;
        }
    };

    const _loadFactoryGates = async () => {
        const response = await PresenterProvider.purchaseInvoicePresenter.getFactoryGates(1, 1000);
        console.log(response, "ressss");
        if (!response.success) {
            showErrorToast(response.message);
        } else {
            factoryGates = response.data.data;
            console.log(obj, factoryGates, "fates");
            if (obj.id > 0) {
                obj.factoryGateId = obj.factoryGateId;
            } else {
                if (factoryGates.length) {
                    obj.factoryGateId = factoryGates[0].id;
                } else {
                    obj.factoryGateId = obj.factoryGateId;
                }
            }
            isLoadingFactoryGates = false;
        }
    };

    const _loadStorageLocations = async () => {
        const response = await PresenterProvider.purchaseInvoicePresenter.getStorageLocations(
            1,
            1000
        );
        if (!response.success) {
            showErrorToast(response.message);
        } else {
            storageLocations = response.data.data;

            isLoadingStorageLocations = false;
        }
    };

    const _loadUsers = async () => {
        const response = await PresenterProvider.userPresenter.getAll(1, 1000);
        console.log(response, "user response");
        if (!response.success) {
            showErrorToast(response.message);
        } else {
            users = response.data.data;
            if (obj.id > 0) {
                obj.purchasedById = obj.purchasedById;
            } else {
                obj.purchasedById = users[0].coreUserId;
            }
            isLoadingUsers = false;
        }
    };

    const handleRejectionReason = (e: Event, index: number) => {
        const target = e.target as HTMLInputElement;
        obj.rawMaterials[index].rejectionReason = target.value.trim();
        if (obj.rawMaterials[index].rejectionReason.length === 0) {
            obj.rawMaterials[index].rejectionReason = null;
        }
    };
    const handleHoldReason = (e: Event, index: number) => {
        const target = e.target as HTMLInputElement;
        obj.rawMaterials[index].holdReason = target.value.trim();
        if (obj.rawMaterials[index].holdReason.length === 0) {
            obj.rawMaterials[index].holdReason = null;
        }
    };

    let selectedItemsMap: Map<string, IPurchaseInvoiceReceivedItemInfo> = new Map();

    const onRawMaterialSelected = (
        data: IRawMaterial,
        item: IPurchaseInvoiceReceivedItemDetails,
        index: number
    ) => {
        if (data) {
            if (
                oldRawMaterialsIds.includes(data.id) ||
                obj.rawMaterials.find((item) => item.rawMaterialId === data.id)
            ) {
                showErrorToast(data.name + " is already added");
                return;
            }

            const gstPercentage = data.gstPercentage;

            item.rawMaterialId = data.id;
            // let price = 0;

            // if (gstPercentage > 0) {
            //     price = Number(
            //         (
            //             parseFloat(data.price.toString()) +
            //             (parseFloat(data.gstPercentage.toString()) / 100) *
            //                 parseFloat(data.price.toString())
            //         ).toFixed(2)
            //     );
            // } else {
            // price = Number(parseFloat(data.price.toString()).toFixed(2));
            // }
            // item.price = Number((price * (item.totalQty ?? 1)).toFixed(2));

            item.price = data.price;
            item.rawMaterial = data.name;
            item.unit = data.unitName;

            selectedItemsMap.set(index.toString(), {
                rawMaterialId: data.id,
                rawMaterial: data.name,
                unitName: data.unitName,
                price: data.price,
            });
        } else {
            oldRawMaterialsIds = oldRawMaterialsIds.filter((id) => id !== item.rawMaterialId);

            // item.totalQty = 0;
            // item.price = 0;
            // item.rawMaterialId = -1;
            // selectedItemsMap.delete(index.toString());

            obj.rawMaterials = obj.rawMaterials.filter(
                (rm) => rm.rawMaterialId !== item.rawMaterialId
            );
            // Recreate selected item map
            selectedItemsMap = new Map();
            obj.rawMaterials.forEach((item, idx) => {
                selectedItemsMap.set(idx.toString(), {
                    price: item.price,
                    rawMaterial: item.rawMaterial,
                    rawMaterialId: item.rawMaterialId,
                    unitName: item.unit,
                });
            });
        }
        obj = obj;
        selectedItemsMap = selectedItemsMap;
    };

    const isItemPriceAcceptable = (item: IPurchaseInvoiceReceivedItem, index: number) => {
        let agreedPrice = 1;
        const data = selectedItemsMap.get(index.toString());
        if (data) {
            agreedPrice = data.price;
            // agreedPrice = Number(
            //     (
            //         parseFloat(data.price.toString()) +
            //         (parseFloat(data.gstPercentage.toString()) / 100) *
            //             parseFloat(data.price.toString())
            //     ).toFixed(2)
            // );
        }

        return item.price * item.totalQty <= agreedPrice * item.totalQty;
    };

    const getSelected = (index: number) => {
        const item = selectedItemsMap.get(index.toString());
        return item
            ? ({
                  id: item.rawMaterialId,
                  name: item.rawMaterial,
                  unitName: item.unitName,
              } as IRawMaterial)
            : null;
    };

    const rejectionDetails: IRejectedItemDetails[] = [];

    let selectedPO: IPurchaseOrder | null = null;

    const _onPOSelection = async (data: IPurchaseOrder | null) => {
        if (!data) {
            obj.poNumber = "";
            selectedPO = null;
            obj.rawMaterials = [];
            obj.supplierId = -1;
            obj.supplier = SupplierUtils.getEmpty();
            selectedSupplier = null;
            selectedItemsMap = new Map();
        } else {
            obj.rawMaterials = [];

            /* set supplier */
            obj.supplierId = data.supplier.id;
            obj.supplier = data.supplier;
            selectedSupplier = data.supplier;

            /* set items */
            for (const element of data.items) {
                obj.rawMaterials.push({
                    rawMaterialId: -1,
                    price: element.item.price,
                    totalQty: element.qty,
                    storageLocationId: null,
                    rejectedQty: 0,
                    rejectionReason: null,
                    rejectedById: null,
                    holdQty: 0,
                    holdReason: null,
                    holdById: null,
                    rawMaterial: element.item.name,
                    unit: element.item.unitName,
                    excessQty: 0,
                    replaceableQty: 0
                });
                onRawMaterialSelected(
                    element.item,
                    obj.rawMaterials[obj.rawMaterials.length - 1],
                    obj.rawMaterials.length - 1
                );
            }

            obj.poNumber = data.poNumber;
            selectedPO = data;
        }
    };

    onMount(() => {
        _loadFactoryGates();
        // _loadStorageLocations();
        _loadUsers();
        if (obj.supplier.id > 0) {
            selectedSupplier = obj.supplier;
            selectedSupplier.id = selectedSupplier.id;
            obj.supplierId = selectedSupplier.id;
        }

        if (obj.id) {
            obj.id = obj.id;
            if (obj.id > 0) {
                obj.invoiceDate = new Date(obj.invoiceDate);
            }
        }

        let index = 0;
        for (const item of obj.rawMaterials) {
            oldRawMaterialsIds.push(item.rawMaterialId);

            selectedItemsMap.set(index.toString(), {
                price: item.price,
                rawMaterial: item.rawMaterial,
                rawMaterialId: item.rawMaterialId,
                unitName: item.unit,
            });

            rejectionDetails.push({
                itemId: item.rawMaterialId,
                rejectedQty:
                    obj.rawMaterialRejections.reduce((sum: any, rejection: any) => {
                        return rejection.rawMaterialId === item.rawMaterialId
                            ? sum + rejection.rejectedQty
                            : sum;
                    }, 0) || 0,
                receivedQty: item.totalQty + item.excessQty,
            });

            index++;
        }
        selectedPO = obj.purchaseOrder;
    });
</script>

{#if isLoadingFactoryGates}
    <PageLoader />
{:else}
    <div class="flex items-center justify-center">
        <div class="w-[90vw] p-2">
            <div class=" flex items-center justify-between py-2">
                <FormHeader label={"Purchase stock"}></FormHeader>
                <BreadCrumbs breadCrumbData={[]} />
            </div>

            <hr class="mb-5" />

            <div class=" grid grid-cols-6 gap-4 mt-5">
                <div>
                    <Label for="invoiceNumber" class="mb-2 font-sans capitalize tracking-[0px]">
                        Invoice No.
                        {#if validationErrors.has("invoiceNumber")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Input
                        disabled={obj.id > 0 && !isFullEdit}
                        type="text"
                        id="invoiceNumber"
                        placeholder="Invoice No."
                        class="uppercase dark:bg-primary-700 {validationErrors.has('invoiceNumber')
                            ? 'border-red-500'
                            : ''}"
                        bind:value={obj.invoiceNumber}
                    />
                    {#if validationErrors.has("invoiceNumber")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("invoiceNumber")}
                        </p>
                    {/if}
                </div>

                <div>
                    <Label for="invoiceDate" class="mb-2 font-sans capitalize tracking-[0px]">
                        Invoice Date
                        {#if validationErrors.has("invoiceDate")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Input
                        disabled={obj.id > 0 && !isFullEdit}
                        type="date"
                        id="invoiceDate"
                        placeholder="Invoice No."
                        class="dark:bg-primary-700 {validationErrors.has('invoiceDate')
                            ? 'border-red-500'
                            : ''}"
                        value={formatDateToInput(obj.invoiceDate)}
                        onchange={(e: any) => {
                            obj.invoiceDate = new Date(e.target.value);
                        }}
                    />
                    {#if validationErrors.has("invoiceDate")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("invoiceDate")}
                        </p>
                    {/if}
                </div>

                <div>
                    <Label for="poNumber" class="mb-2 font-sans capitalize tracking-[0px]">
                        PO/DO Number
                        {#if validationErrors.has("poNumber")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>

                    {#if obj.id > 0 && selectedPO === null}
                        <Input
                            disabled={obj.id > 0 && !isFullEdit}
                            id="poNumber"
                            type={"text"}
                            class="uppercase dark:bg-primary-700 {validationErrors.has('poNumber')
                                ? 'border-red-500'
                                : ''}"
                            bind:value={obj.poNumber}
                        />
                    {:else}
                        <PoSearch
                            disabled={obj.id > 0}
                            onInput={(value) => {
                                obj.poNumber = value;
                            }}
                            selected={selectedPO}
                            onSelected={(data) => {
                                _onPOSelection(data);
                            }}
                        />
                    {/if}

                    {#if validationErrors.has("poNumber")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("poNumber")}
                        </p>
                    {/if}
                </div>

                <div>
                    <Label for="poDate" class="mb-2 font-sans capitalize tracking-[0px]">
                        PO Date
                        {#if validationErrors.has("poDate")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>
                    <Input
                        disabled={obj.id > 0 && !isFullEdit}
                        type="date"
                        id="poDate"
                        placeholder="Invoice No."
                        class="dark:bg-primary-700 {validationErrors.has('poDate')
                            ? 'border-red-500'
                            : ''}"
                        value={formatDateToInput(obj.poDate)}
                        onchange={(e: any) => {
                            obj.poDate = new Date(e.target.value);
                        }}
                    />
                    {#if validationErrors.has("poDate")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("poDate")}
                        </p>
                    {/if}
                </div>

                <div>
                    <Label for="factoryGateId" class="mb-2 font-sans capitalize tracking-[0px]">
                        Factory Gate
                        {#if validationErrors.has("factoryGateId")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>

                    <FactoryGateDropdown
                        disabled={obj.id > 0 && !isFullEdit}
                        data={factoryGates}
                        id="factoryGateId"
                        cssClass="dark:bg-primary-700 {validationErrors.has('factoryGateIdId')
                            ? 'border-red-500'
                            : ''}"
                        selectedValue={obj.factoryGateId}
                        onSelected={(data) => {
                            obj.factoryGateId = Number(data);
                        }}
                    />

                    {#if validationErrors.has("factoryGateIdId")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("factoryGateId")}
                        </p>
                    {/if}
                </div>

                <div>
                    <Label for="purchasedById" class="mb-2 font-sans capitalize tracking-[0px]">
                        Purchased By
                        {#if validationErrors.has("purchasedById")}
                            <span class="text-red-600">*</span>
                        {/if}
                    </Label>

                    <UsersDropdown
                        disabled={obj.id > 0 && !isFullEdit}
                        data={users}
                        id="purchasedById"
                        cssClass="dark:bg-primary-700 {validationErrors.has('purchasedByIdId')
                            ? 'border-red-500'
                            : ''}"
                        selectedValue={obj.purchasedById}
                        onSelected={(data) => {
                            obj.purchasedById = Number(data.toString());
                        }}
                    />

                    {#if validationErrors.has("purchasedByIdId")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("purchasedById")}
                        </p>
                    {/if}
                </div>
            </div>
            <div class="m-2"></div>

            <div class=" grid grid-cols-4 mt-5 gap-3">
                <div>
                    <SupplierSearch
                        disabled={(obj.id > 0 && !isFullEdit) || selectedPO !== null}
                        selected={selectedSupplier}
                        onSelected={(data) => {
                            if (data) {
                                obj = { ...obj, supplierId: data.id };
                                obj.supplierId = data.id;
                                obj.rawMaterials = [
                                    {
                                        rawMaterialId: -1,
                                        price: 0,
                                        totalQty: 0,
                                        storageLocationId: null,
                                        rejectedQty: 0,
                                        rejectionReason: null,
                                        rejectedById: null,
                                        holdQty: 0,
                                        holdReason: null,
                                        holdById: null,
                                        rawMaterial: "",
                                        unit: "",
                                        excessQty: 0,
                                        replaceableQty: 0
                                    },
                                ];
                            } else {
                                obj.supplierId = -1;
                                obj.rawMaterials = [];
                                selectedPO = null;
                            }
                            selectedSupplier = data;
                        }}
                    />

                    {#if validationErrors.has("supplierId")}
                        <p class="pt-2 font-serif text-[14px] italic text-red-500">
                            {validationErrors.get("supplierId")}
                        </p>
                    {/if}
                </div>
                {#if isUserHasEditPermission && obj.id > 0 && $userPermissions.includes(AppPermissions.RAW_MATERIAL_STOCK.UPDATE_PURCHASE_FULL) && !isFullEdit}
                    <div>
                        <Label
                            for="fullEditToggle"
                            class="mb-2 font-sans capitalize tracking-[0px]"
                        >
                            Full Edit?
                        </Label>
                        <input
                            type="checkbox"
                            id="fullEditToggle"
                            bind:checked={isFullEdit}
                            class="toggle toggle-primary"
                        />
                    </div>
                {/if}
            </div>

            <div class="m-2"></div>
            <span class="text-sm italic">Items</span>
            <div class="m-2"></div>
            <div class="overflow-x-auto">
                <table
                    class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right border"
                >
                    <thead
                        class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
                    >
                        <tr>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">SR No.</th>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">Item</th>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">Unit</th>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">
                                Total Qty
                            </th>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">
                                Excess Qty
                            </th>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">
                                Replaced Qty
                            </th>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">
                                Unit Price
                            </th>
                            <th scope="col" class="px-6 py-3 text-sm" style="white-space: nowrap;">
                                Total Price
                            </th>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">
                                Rejected Qty
                            </th>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">
                                Rejected reason
                            </th>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">
                                Hold Qty
                            </th>
                            <th scope="col" class="px-6 py-3 text-sm whitespace-nowrap">
                                Hold reason
                            </th>
                            <th scope="col" class="px-6 py-3">X</th>
                        </tr>
                    </thead>
                    <tbody>
                        {#each obj.rawMaterials as item, index}
                            <tr
                                class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                            >
                                <td class="px-6 py-4">
                                    {index + 1}
                                </td>
                                <td class="px-6 py-4 w-[300px] h-[60px]">
                                    <div class="mb-[35px] w-[250px]">
                                        <SupplierRawMaterialSearch
                                            disabled={(oldRawMaterialsIds.includes(
                                                item.rawMaterialId
                                            ) &&
                                                !isFullEdit) ||
                                                selectedPO !== null}
                                            {selectedSupplier}
                                            supplierId={obj.supplierId}
                                            selected={getSelected(index)}
                                            onSelected={(data) => {
                                                onRawMaterialSelected(data, item, index);
                                            }}
                                        />
                                    </div>
                                </td>

                                <td class="px-6 py-4">
                                    <Input
                                        disabled
                                        class="mb-[35px] w-[100px]"
                                        type="text"
                                        value={selectedItemsMap.get(index.toString())?.unitName ??
                                            ""}
                                    />
                                </td>
                                <td class="px-6 py-4">
                                    <Input
                                        disabled={oldRawMaterialsIds.includes(item.rawMaterialId) &&
                                            !isFullEdit}
                                        class="mb-[35px] w-[100px]"
                                        type="number"
                                        value={item.totalQty}
                                        on:input={(e) => {
                                            handleTotalQty(e, index);
                                        }}
                                    />
                                </td>
                                <td class="px-6 py-4">
                                    <Input
                                      
                                        class="mb-[35px] w-[100px]"
                                        type="number"
                                        value={item.excessQty}
                                        on:input={(e) => {
                                            handleExcessQty(e, index)
                                        }}
                                    />
                                </td>
                                <td class="px-6 py-4">
                                    <Input
                                        
                                        class="mb-[35px] w-[100px]"
                                        type="number"
                                        value={item.replaceableQty}
                                        on:input={(e) => {
                                            handleReplacedQty(e,index)
                                        }}
                                    />
                                </td>

                                <td class="px-6 py-4">
                                    <Input
                                        disabled={oldRawMaterialsIds.includes(item.rawMaterialId) &&
                                            !isFullEdit}
                                        class="mb-[35px]
                                    w-[100px]

                                        {isItemPriceAcceptable(item, index)
                                            ? ''
                                            : '!border-red-500'}
                                        "
                                        type="number"
                                        value={item.price}
                                        on:change={(e) => {
                                            handlePrice(e, index);
                                        }}
                                    />
                                </td>

                                <td class="px-6 py-4">
                                    <Input
                                        disabled
                                        class="mb-[35px]
                                    w-[100px]

                                        {isItemPriceAcceptable(item, index)
                                            ? ''
                                            : '!border-red-500'}
                                        "
                                        type="number"
                                        value={Number((item.price * item.totalQty).toFixed(2))}
                                    />
                                </td>

                                <td class="px-6 py-4">
                                    <Input
                                        disabled={oldRawMaterialsIds.includes(item.rawMaterialId) &&
                                            !isFullEdit}
                                        class="mb-[35px] w-[100px]"
                                        type="number"
                                        value={item.rejectedQty}
                                        on:change={(e) => {
                                            handleRejectedQty(e, index);
                                        }}
                                    />
                                </td>
                                <td class="px-6 py-4">
                                    <Input
                                        disabled={oldRawMaterialsIds.includes(item.rawMaterialId) &&
                                            !isFullEdit}
                                        class="mb-[35px] w-[100px]"
                                        type="text"
                                        value={item.rejectionReason ?? ""}
                                        on:change={(e) => {
                                            handleRejectionReason(e, index);
                                        }}
                                    />
                                </td>
                                <td class="px-6 py-4">
                                    <Input
                                        disabled={oldRawMaterialsIds.includes(item.rawMaterialId) &&
                                            !isFullEdit}
                                        class="mb-[35px]"
                                        type="number"
                                        value={item.holdQty}
                                        on:change={(e) => {
                                            handleHoldQty(e, index);
                                        }}
                                    />
                                </td>
                                <td class="px-6 py-4">
                                    <Input
                                        disabled={oldRawMaterialsIds.includes(item.rawMaterialId) &&
                                            !isFullEdit}
                                        class="mb-[35px] w-[100px]"
                                        type="text"
                                        value={item.holdReason ?? ""}
                                        on:change={(e) => {
                                            handleHoldReason(e, index);
                                        }}
                                    />
                                </td>

                                <td class="px-6 py-4">
                                    {#if obj.rawMaterials.length > 1 && (isFullEdit || !oldRawMaterialsIds.includes(item.rawMaterialId))}
                                        <CloseButton
                                            class="w-content mb-[35px]"
                                            on:click={() => {
                                                obj.rawMaterials.splice(index, 1);
                                                // Recreate selected item map
                                                selectedItemsMap = new Map();
                                                obj.rawMaterials.forEach((item, idx) => {
                                                    selectedItemsMap.set(idx.toString(), {
                                                        price: item.price,
                                                        rawMaterial: item.rawMaterial,
                                                        rawMaterialId: item.rawMaterialId,
                                                        unitName: item.unit,
                                                    });
                                                });

                                                oldRawMaterialsIds = oldRawMaterialsIds.filter(
                                                    (id) => id !== item.rawMaterialId
                                                );
                                                obj.rawMaterials = obj.rawMaterials;
                                            }}
                                        />
                                    {/if}
                                </td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            </div>

            <div class="m-2"></div>

            <div class="mt-5 flex w-full justify-between">
                <div>
                    {#if isUserHasEditPermission && obj.rawMaterials.length > 0 && selectedPO === null}
                        <CustomButton
                            onClick={() => {
                                obj.rawMaterials.push({
                                    rawMaterialId: -1,
                                    price: 0,
                                    totalQty: 0,
                                    storageLocationId: null,
                                    rejectedQty: 0,
                                    rejectionReason: null,
                                    rejectedById: null,
                                    holdQty: 0,
                                    holdReason: null,
                                    holdById: null,
                                    rawMaterial: "",
                                    unit: "",
                                    excessQty: 0,
                                    replaceableQty: 0
                                });
                                obj = obj;
                            }}
                            cssClass=" bg-black"
                            title={"Add new item"}
                            isLoading={isDoingTask}
                        />
                    {/if}
                </div>
                {#if isUserHasEditPermission}
                    <CustomButton
                        onClick={handleSubmit}
                        cssClass="w-32 bg-black"
                        title={"Save"}
                        isLoading={isDoingTask}
                    />
                {/if}
            </div>

            {#if isUserHasEditPermission && obj.id > 0}
                <ManualDebitNotes
                    purchaseInv={obj}
                    debitNotes={obj.debitNotes}
                    rawMaterials={obj.rawMaterials.map((item) => ({
                        id: item.rawMaterialId,
                        title: item.rawMaterial,
                    }))}
                    rejectionData={rejectionDetails}
                />
            {/if}
        </div>
    </div>
{/if}
