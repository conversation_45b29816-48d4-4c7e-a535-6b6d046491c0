// Base response interfaces
export interface BaseResponse {
    success: boolean;
    message: string;
}

export interface ErrorResponse extends BaseResponse {
    success: false;
    data: null;
    errors?: Array<{
        field: string;
        message: string;
    }>;
}

// Pagination interfaces
export interface PaginationInfo {
    currentPage: number;
    totalPages: number;
    totalData: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
}

export interface LogFilters {
    method?: string;
    statusCode?: number;
    hasError?: boolean;
    url?: string;
    ip?: string;
}

// Available dates response
export interface AvailableLogDatesResponse extends BaseResponse {
    success: true;
    data: string[]; // Array of dates in YYYY-MM-DD format (most recent first)
}

// Log entry interfaces
export interface IMorganLog {
    timestamp: string; // ISO 8601 timestamp
    id: string; // Unique UUID for the log entry
    ip: string; // Client IP address
    request: {
        timestamp: string;
        method: string; // HTTP method (GET, POST, etc.)
        url: string; // Request URL
        headers: Record<string, any>; // Request headers
        body: any; // Request body (if any)
    };
    response: {
        status: number; // HTTP status code
        headers: Record<string, any>; // Response headers
        body: any; // Response body
        responseTime: string; // Response time in "XXXms" format
    };
    error: false | {
        message: string;
        name?: string;
        stack?: string;
        statusCode?: number;
        timestamp?: string;
        details?: any;
    };
}

// Paginated logs by date response
export interface PaginatedLogsByDateResponse extends BaseResponse {
    success: true;
    data: {
        logs: IMorganLog[];
        pagination: PaginationInfo;
        filters: {
            date: string;
            method?: string;
            statusCode?: number;
            hasError?: boolean;
            url?: string;
            ip?: string;
        };
    };
}

// Legacy logs by date response (for backwards compatibility)
export interface LogsByDateResponse extends BaseResponse {
    success: true;
    data: IMorganLog[];
}

// Log statistics interfaces
export interface LogStatistics {
    date: string;
    totalRequests: number;
    requestsByMethod: Record<string, number>; // e.g., {"GET": 150, "POST": 25}
    requestsByStatus: Record<string, number>; // e.g., {"200": 120, "404": 5, "500": 2}
    errorCount: number;
    averageResponseTime: number; // in milliseconds
    slowestRequests: Array<{
        url: string;
        method: string;
        responseTime: number;
        timestamp: string;
    }>;
    topIPs: Array<{
        ip: string;
        count: number;
    }>;
    errorDistribution: Record<string, number>; // Error types and their counts
    timeRange: {
        start: string; // ISO 8601 timestamp
        end: string; // ISO 8601 timestamp
    };
}

export interface LogStatisticsResponse extends BaseResponse {
    success: true;
    data: LogStatistics;
}

// Union types for API responses
export type LogsApiResponse = 
    | AvailableLogDatesResponse 
    | PaginatedLogsByDateResponse
    | LogsByDateResponse 
    | LogStatisticsResponse 
    | ErrorResponse;

// Utility types for component state
export interface LogsState {
    availableDates: string[];
    selectedDate: string;
    logs: IMorganLog[];
    pagination: PaginationInfo | null;
    statistics: LogStatistics | null;
    loading: boolean;
    error: string | null;
    filters: LogFilters;
}

// Filter and search interfaces (keeping for backwards compatibility)
export interface LogSearchOptions {
    searchTerm?: string;
    filters?: LogFilters;
    sortBy?: 'timestamp' | 'responseTime' | 'status';
    sortDirection?: 'asc' | 'desc';
}

// Utility functions
export function isErrorLog(log: IMorganLog): boolean {
    return log.error !== false || log.response.status >= 400;
}

export function getResponseTimeInMs(responseTime: string): number {
    return parseInt(responseTime.replace('ms', ''));
}

export function formatTimestamp(timestamp: string): string {
    return new Date(timestamp).toLocaleString();
}

export function getStatusClass(status: number): string {
    if (status >= 200 && status < 300) return 'text-green-600';
    if (status >= 300 && status < 400) return 'text-yellow-600';
    if (status >= 400 && status < 500) return 'text-orange-600';
    if (status >= 500) return 'text-red-600';
    return 'text-gray-600';
}

export function getMethodClass(method: string): string {
    switch (method.toUpperCase()) {
        case 'GET': return 'bg-blue-100 text-blue-800';
        case 'POST': return 'bg-green-100 text-green-800';
        case 'PUT': return 'bg-yellow-100 text-yellow-800';
        case 'DELETE': return 'bg-red-100 text-red-800';
        case 'PATCH': return 'bg-purple-100 text-purple-800';
        default: return 'bg-gray-100 text-gray-800';
    }
} 