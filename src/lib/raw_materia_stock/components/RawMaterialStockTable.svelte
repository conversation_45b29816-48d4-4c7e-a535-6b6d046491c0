<script lang="ts">
    import { Spinner } from "flowbite-svelte";
    import { capitalizeFirstWord, debounce, showErrorToast } from "$lib/common/utils/common-utils";
    import { onMount } from "svelte";
    import Loader from "$lib/common/components/Loader.svelte";
    import type { IRawMaterialStock } from "../models/IRawMaterialStock";
    import type { PaginatedDataWrapper } from "$lib/common/models/base_model";
    import PaginationButtons from "$lib/common/components/PaginationButtons.svelte";
    import { PresenterProvider } from "$lib/PresenterProvider";
    import ItemCategorySearch from "$lib/item_category/components/ItemCategorySearch.svelte";
    import type { IdTitle } from "$lib/IdTitle/models/IdTitle";
    import { exportCurrentStock } from "../utils/RawMaterialStockUtils";

    export let paginationData: PaginatedDataWrapper<IRawMaterialStock>;
    export let searchTerm: string = "";

    let isLoading: boolean = false;
    let searchLoading: boolean = false;
    let isSearching: boolean = false;
    let filteredData: IRawMaterialStock[] = [];
    let alreadyFetchedData: IRawMaterialStock[] = [];
    let categoryIdToExport = -1;
    let selectedCategory: IdTitle | null = null;
    let isExporting = false;

    let debounceSearch = debounce(async (e: any) => {
        if (e.target.value.trim().length > 2) {
            // filteredData = paginationData.pagination.data.filter((data) =>
            //     data.rawMaterialName.toLowerCase().includes(searchTerm.toLowerCase())
            // );
            filteredData = [];
            if (filteredData.length === 0) {
                isSearching = true;
                const result =
                    await PresenterProvider.rawMaterialStockPresenter.searchByRawMaterial(
                        e.target.value.trim()
                    );
                if (!result.success) {
                    return showErrorToast(result.message);
                } else {
                    filteredData = result.data.data;
                    isSearching = false;
                }
            }
        } else {
            filteredData = alreadyFetchedData;
            // onSearchClear();
        }
    }, 300);

    onMount(() => {
        filteredData = paginationData.pagination.data;
        alreadyFetchedData = paginationData.pagination.data;
    });
</script>

{#if isLoading}
    <div class="flex h-[80vh] w-[100%] items-center justify-center">
        <Spinner size={"10"} color="gray" />
    </div>
{:else}
    <h1
        class="mb-4 font-primary text-2xl font-medium capitalize tracking-[0px] text-black dark:text-gray-100"
    >
        Current Stock
    </h1>

    <div class="relative overflow-x-auto font-primary shadow-md sm:rounded-lg">
        <div
            class="flex-column flex flex-wrap items-center space-y-4 rounded-lg bg-[#E3E3E3] p-4 text-sm focus:ring-primary-500 dark:bg-[#131313] md:flex-row md:space-y-0
                justify-between
            
            "
        >
            <label for="table-search" class="sr-only">Search</label>

            <div class="relative">
                <div
                    class="rtl:inset-r-0 pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3"
                >
                    <img src="/images/svg/search.svg" alt="l" width="15px" />
                </div>
                <div>
                    <input
                        type="text"
                        id="franchise-search"
                        bind:value={searchTerm}
                        on:input={debounceSearch}
                        class="block w-96 rounded-lg p-2 ps-10 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:bg-primary-800 dark:text-white"
                        placeholder="Search by raw material name"
                    />
                </div>

                {#if isSearching}
                    <div class="absolute right-0 top-0 bottom-0 flex items-center justify-center">
                        <Loader />
                    </div>
                {/if}
            </div>
            <div class="flex space-x-4 items-center">
                <div class="w-[300px]">
                    <ItemCategorySearch
                        labelText="Select category to export"
                        onSelected={(data) => {
                            if (data) {
                                categoryIdToExport = data.id;
                                selectedCategory = {
                                    id: data.id,
                                    title: data.name,
                                };
                            } else {
                                categoryIdToExport = -1;
                                selectedCategory = null;
                            }
                        }}
                        selected={selectedCategory}
                    />
                </div>

                <button
                    id="dropdownActionButton"
                    data-dropdown-toggle="dropdownAction"
                    class="inline-flex items-center rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-sm font-medium text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:border-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-700 h-[44px] mt-6"
                    type="button"
                    disabled={isExporting}
                    on:click={async () => {
                        if (!selectedCategory) {
                            return showErrorToast("Please select a category");
                        }
                        isExporting = true;
                        await exportCurrentStock(selectedCategory);
                        isExporting = false;
                    }}
                >
                    {#if isExporting}
                        <div class="flex gap-2 items-center justify-center">
                            Loading <Spinner class="size-5" />
                        </div>
                    {:else}
                        Export
                    {/if}
                </button>
            </div>
        </div>

        {#if isSearching}
            <div class="flex h-[30rem] w-full items-center justify-center">
                <Loader />
            </div>
        {:else}
            <table class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right">
                <thead
                    class="bg-gray-50 font-primary text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
                >
                    <tr>
                        <th scope="col" class="px-6 py-3">SR No.</th>
                        <th scope="col" class="px-6 py-3">Raw Material</th>
                        <th scope="col" class="px-6 py-3">SKU</th>
                        <th scope="col" class="px-6 py-3">Total Stock</th>
                        <th scope="col" class="px-6 py-3">Usable Stock</th>
                    </tr>
                </thead>
                <tbody>
                    {#each filteredData as row, index}
                        <tr
                            class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
                        >
                            <td class="px-6 py-4">
                                <a
                                    href={"####/admin/raw-materials-stock/edit?id=" + row.id}
                                    class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                    {(paginationData.pagination.currentPage - 1) *
                                        paginationData.pageSize +
                                        index +
                                        1}
                                </a>
                            </td>
                            <td class="px-6 py-4">
                                <a
                                    href={"####/admin/raw-materials-stock/edit?id=" + row.id}
                                    class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                    {capitalizeFirstWord(row.rawMaterialName)}
                                </a>
                            </td>
                            <td class="px-6 py-4">
                                <a
                                    href={"####/admin/raw-materials-stock/edit?id="+row.id}
                                    class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                    {row.sku}
                                </a>
                            </td>
                            <td class="px-6 py-4">
                                <a
                                    href={"####/admin/raw-materials-stock/edit?id=" + row.id}
                                    class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                    {row.totalStock}
                                </a>
                            </td>
                            <td class="px-6 py-4">
                                <a
                                    href={"####/admin/raw-materials-stock/edit?id=" + row.id}
                                    class="block h-full w-full whitespace-nowrap font-medium"
                                >
                                    {row.usableStock}
                                </a>
                            </td>
                        </tr>
                    {/each}
                    {#if filteredData.length === 0}
                        <tr class="font-medium text-black dark:text-gray-400">
                            <td colspan="6" class="h-[50vh] text-center">
                                {searchTerm.trim().length > 0 ? "No results found" : "Yet no stock"}
                            </td>
                        </tr>
                    {/if}
                </tbody>
            </table>
        {/if}
    </div>
    <PaginationButtons {paginationData} />
{/if}
