import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import type { DTO } from "$lib/common/models/BaseDTO";
import type { IReceiveRawMaterialStock, IRawMaterialStock, IRawMaterialStockInDetails, IRawMaterialStockUpdateRequest, IAssignStorageToStock, IRawMaterialStockIssuance, IRawMaterialStockIssuanceResponse } from "../models/IRawMaterialStock";

export interface IRawMaterialStockRepo {
    getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStock>>>;



    searchByRawMaterial(rawMaterialName: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialStock>>>;
    receive(payload: IReceiveRawMaterialStock): Promise<DTO<null>>;
    stockInEntries(page: number, pageSize: number,text?:string,startDate?:Date,endDate?:Date): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>>;

    searchStockInByText(text: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>>;

    searchStockInByTextWithoutStorage(text: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>>;

    stockInEntriesByDate(
        startDate: string,
        endDate: string,
        page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>>;


    update(payload: IRawMaterialStockUpdateRequest): Promise<DTO<null>>;

    getById(id: number): Promise<DTO<IRawMaterialStock>>;

    getByRawMaterialId(rawMaterialId: number): Promise<DTO<IRawMaterialStock>>;

    getStockInEntriesWithoutStorageLocation(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>>;


    getStockInEntryById(id: number): Promise<DTO<IRawMaterialStockInDetails>>;


    assignStorageLocation(payload: IAssignStorageToStock): Promise<DTO<null>>;

    issueStock(payload: IRawMaterialStockIssuance): Promise<DTO<null>>;

    editStockIssuance(payload: IRawMaterialStockIssuance): Promise<DTO<null>>;

    getAllIssuedStock(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockIssuanceResponse>>>;
    getStockIssuanceByEntryId(id:string): Promise<DTO<IRawMaterialStockIssuanceResponse>>;

    searchIssuedByText(text: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockIssuanceResponse>>>;

    exportCurrentStock(categoryId:number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>>;
}