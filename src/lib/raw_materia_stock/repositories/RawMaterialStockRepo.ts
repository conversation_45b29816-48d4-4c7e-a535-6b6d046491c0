import { ITEM_UNITS_API_PATH, RAW_MATERIAL_API_PATH, RAW_MATERIAL_STOCK_API_PATH } from "$lib/common/configs/serverConfig";
import {
    getHandledErrorDTO,
    getSuccessDTO,
    getUnhandledErrorDTO,
    type DTO,
} from "$lib/common/models/BaseDTO";
import { fetchData } from "$lib/fetch/utils/fetch-utils";
import type { FetchResult } from "$lib/fetch/models/Fetch";
import { handleError } from "$lib/common/utils/logging";
import type { IRawMaterialStockRepo } from "./IRawMaterialStockRepo";
import type { IReceiveRawMaterialStock, IRawMaterialStock, IRawMaterialStockInDetails, IRawMaterialStockUpdateRequest, IAssignStorageToStock, IRawMaterialStockIssuance, IRawMaterialStockIssuanceResponse } from "../models/IRawMaterialStock";
import type { PaginatedBaseResponse } from "$lib/common/models/base_model";
import { parseCreateStockIssuance } from "../utils/RawMaterialStockUtils";

export class RawMaterialStockRepo implements IRawMaterialStockRepo {
   
    private _apiPath: string = RAW_MATERIAL_STOCK_API_PATH;
    async getStockIssuanceByEntryId(id: string): Promise<DTO<IRawMaterialStockIssuanceResponse>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<IRawMaterialStockIssuanceResponse> = await fetchData(this._apiPath + "/stock/issuance/" + id, options);
            console.log(res,"&&")
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }
    async getById(id: number): Promise<DTO<IRawMaterialStock>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<IRawMaterialStock> = await fetchData(this._apiPath + "/" + id, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getByRawMaterialId(id: number): Promise<DTO<IRawMaterialStock>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<IRawMaterialStock> = await fetchData(this._apiPath + "/stock/by-raw-material-id/" + id, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async update(payload: IRawMaterialStockUpdateRequest): Promise<DTO<null>> {
        try {
            const options = {
                method: "PUT",
                body: payload,
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/update/" + payload.rawMaterialId,
                options
            );
            if (res.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }


    async receive(payload: IReceiveRawMaterialStock): Promise<DTO<null>> {
        try {
            const options = {
                method: "POST",
                body: payload,
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/receive",
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getAll(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStock>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IRawMaterialStock>> = await fetchData(this._apiPath + "/?page=" + page + "&pageSize=" + pageSize, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }



    async searchByRawMaterial(rawMaterialName: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialStock>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IRawMaterialStock>> = await fetchData(this._apiPath + "/stock/search?rawMaterialName=" + rawMaterialName, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }


    async stockInEntries(page: number, pageSize: number, text?: string, startDate?: Date, endDate?: Date): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>> {
        try {
            const options = {
                method: "GET",
            };
            let query = "page=" + page + "&pageSize=" + pageSize;

            if (text) {
                query = query + "&text=" + text;
            }
            if (startDate) {
                query = query + "&startDate=" + startDate;
            }
            if (endDate) {
                query = query + "&endDate=" + endDate;
            }

            const res: FetchResult<PaginatedBaseResponse<IRawMaterialStockInDetails>> = await fetchData(this._apiPath + "/stock-in?" + query, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async searchStockInByText(text: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IRawMaterialStockInDetails>> = await fetchData(this._apiPath + "/stock-in/searchByText?text=" + text, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async searchStockInByTextWithoutStorage(text: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IRawMaterialStockInDetails>> = await fetchData(this._apiPath + "/stock-in/searchByTextWithoutStorage?text=" + text, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async stockInEntriesByDate(
        startDate: string,
        endDate: string,
        page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IRawMaterialStockInDetails>> = await fetchData(this._apiPath + "/stock-in-date-range?startDate=" + startDate.toString() + "&endDate=" + endDate.toString() + "&page=" + page + "&pageSize=" + pageSize, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getStockInEntriesWithoutStorageLocation(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IRawMaterialStockInDetails>> = await fetchData(this._apiPath + "/stock-in-not-assigned?page=" + page + "&pageSize=" + pageSize, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async getStockInEntryById(id: number): Promise<DTO<IRawMaterialStockInDetails>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<IRawMaterialStockInDetails> = await fetchData(this._apiPath + "/stock-in/" + id, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }


    async assignStorageLocation(payload: IAssignStorageToStock): Promise<DTO<null>> {
        try {
            const options = {
                method: "PUT",
                body: payload,
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/stock-in/" + payload.id,
                options
            );
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async issueStock(payload: IRawMaterialStockIssuance): Promise<DTO<null>> {
        try {
            const options = {
                method: "POST",
                body: parseCreateStockIssuance(payload),
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/stock/issue",
                options
            );
            if (res.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async editStockIssuance(payload: IRawMaterialStockIssuance): Promise<DTO<null>> {
        try {
            const options = {
                method: "PUT",
                body: parseCreateStockIssuance(payload),
            };
            const res: FetchResult<null> = await fetchData(
                this._apiPath + "/stock/issuance/" + payload.entryId,
                options
            );
            if (res.success) {
                return getSuccessDTO(null);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }


    async getAllIssuedStock(page: number, pageSize: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockIssuanceResponse>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IRawMaterialStockIssuanceResponse>> = await fetchData(this._apiPath + "/stock/issuance?page=" + page + "&pageSize=" + pageSize, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async searchIssuedByText(text: string): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockIssuanceResponse>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IRawMaterialStockIssuanceResponse>> = await fetchData(this._apiPath + "/stock/issuance/searchByText?text=" + text, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

    async exportCurrentStock(categoryId: number): Promise<DTO<PaginatedBaseResponse<IRawMaterialStockInDetails>>> {
        try {
            const options = {
                method: "GET",
            };
            const res: FetchResult<PaginatedBaseResponse<IRawMaterialStockInDetails>> = await fetchData(this._apiPath + "/export-by-category/with-price/" + categoryId, options);
            // const res: FetchResult<PaginatedBaseResponse<IRawMaterialStockInDetails>> = await fetchData(this._apiPath + "/export-by-category/" + categoryId, options);
            if (res.success) {
                return getSuccessDTO(res.data!);
            } else {
                return getHandledErrorDTO(res.message);
            }
        } catch (error: any) {
            handleError(error);
            return getUnhandledErrorDTO(error, error);
        }
    }

}
