import { Request, Response, NextFunction } from "express";
import { RepoProvider } from "../core/RepoProvider";
import { REDIS_KEYS } from "../features/redis/repositories/RedisServerRepository";




const INACTIVE_SESSION_TIMEOUT = 10 * 60 * 1000;

export const sessionAuthMiddleware = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {

        /* from auth middleware */
        const userId = (req as any).user_id as string;

        const now = Date.now();

        /* Get last activity timestamp from Redis */
        const lastActivity = await RepoProvider.redisServerRepository.get(REDIS_KEYS.LAST_ACTIVITY_TIME + userId);
        const lastActivityTime = lastActivity ? Number(lastActivity) : null;
        const reqUrl: string = req.url;
        /* If last activity exists, check for inactivity timeout */
        if (reqUrl !== "/users/getByFirebaseToken" && lastActivityTime !== null && (now - lastActivityTime) > INACTIVE_SESSION_TIMEOUT) {
            console.log("Session expired for userId = " + userId + " due to inactivity.");
            await RepoProvider.redisServerRepository.delete(REDIS_KEYS.LAST_ACTIVITY_TIME + userId);
            res.status(401).send("Session expired due to inactivity. Please login again.");
            return;
        }

        /* update*/
        await RepoProvider.redisServerRepository.set(REDIS_KEYS.LAST_ACTIVITY_TIME + userId, now.toString());
        next();
    } catch (error) {
        console.error("Session middleware error", error);
        res.status(500).send("Something went wrong");
        return;
    }
};

