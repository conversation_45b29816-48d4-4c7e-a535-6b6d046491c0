import * as cron from 'node-cron';
import { EmailService, IEmailConfig } from '../services/EmailService';
import { RepoProvider } from '../core/RepoProvider';
import { IRawMaterialStockDetails } from '../features/raw_material_stock/models/IRawMaterialStock';

export interface IProblematicStocksJobConfig {
    emailRecipients: string[];
    cronPattern?: string;
    timezone?: string;
    enabled?: boolean;
}

export class ProblematicStocksCronJob {
    private emailService: EmailService;
    private isRunning: boolean = false;
    private recipients: string[];

    constructor(config: IProblematicStocksJobConfig) {
        // Initialize email service with Resend configuration
        const emailConfig: IEmailConfig = {
            apiKey: process.env.RESEND_API_KEY || '',
            fromEmail: process.env.EMAIL_FROM || '<EMAIL>',
        };

        this.emailService = new EmailService(emailConfig);
        this.recipients = config.emailRecipients;
    }

    /**
     * Start the cron job to run daily at 9:00 AM
     * Cron pattern: '0 9 * * *' = At 9:00 AM every day
     * You can change this pattern as needed:
     * - '0 9 * * *' = 9:00 AM daily
     * - '0 8 * * 1-5' = 8:00 AM on weekdays only
     * - '0 star/12 * * *' = Every 12 hours
     */
    public startCronJob(): void {
        console.log('🕘 Starting Problematic Stocks Cron Job...');
        
        // Schedule to run daily at 9:00 AM
        cron.schedule('0 22 * * *', async () => {
            if (this.isRunning) {
                console.log('⏳ Previous job is still running, skipping this execution...');
                return;
            }

            this.isRunning = true;
            console.log(`\n🔄 Running problematic stocks check at ${new Date().toLocaleString()}`);
            
            try {
                await this.checkAndSendProblematicStocksReport();
            } catch (error) {
                console.error('💥 Error in problematic stocks cron job:', error);
            } finally {
                this.isRunning = false;
                console.log('✅ Problematic stocks check completed\n');
            }
        }, {
            timezone: 'Asia/Kolkata' // Adjust timezone as needed
        });

        console.log('✅ Problematic Stocks Cron Job scheduled successfully');
        console.log('📅 Next execution: Daily at 9:00 AM IST');
    }

    /**
     * Run the check immediately (for testing purposes)
     */
    public async runNow(): Promise<void> {
        if (this.isRunning) {
            console.log('⏳ Job is already running...');
            return;
        }

        this.isRunning = true;
        console.log(`\n🔄 Running problematic stocks check manually at ${new Date().toLocaleString()}`);
        
        try {
            await this.checkAndSendProblematicStocksReport();
        } catch (error) {
            console.error('💥 Error in manual problematic stocks check:', error);
        } finally {
            this.isRunning = false;
            console.log('✅ Manual problematic stocks check completed\n');
        }
    }

    /**
     * Main method to check for problematic stocks and send email report
     */
    private async checkAndSendProblematicStocksReport(): Promise<void> {
        try {
            console.log('🔍 Fetching problematic stocks from database...');
            
            // Get problematic stocks using the existing repository method
            const result = await RepoProvider.rawMaterialStockRepo.getProblematicStocks();
            
            if (!result.success) {
                console.error('❌ Failed to fetch problematic stocks:', result.message);
                return;
            }

            const problematicStocks = result.data || [];
            console.log(`📊 Found ${problematicStocks.length} problematic stock entries`);

            // If no problematic stocks found, don't send email
            if (problematicStocks.length === 0) {
                console.log('✅ No problematic stocks found. No email will be sent.');
                return;
            }

            // Log details of problematic stocks
            console.log('📋 Problematic stocks details:');
            problematicStocks.forEach((stock, index) => {
                const issues = this.identifyStockIssues(stock);
                console.log(`  ${index + 1}. ${stock.rawMaterialName} (${stock.sku}) - Issues: ${issues.join(', ')}`);
            });

            // Send email report
            console.log('📧 Sending email report...');
            const emailResult = await this.emailService.sendProblematicStocksReport(
                problematicStocks,
                this.recipients
            );

            if (emailResult.success) {
                console.log('✅ Problematic stocks report sent successfully!');
                console.log(`📧 Email ID(s): ${emailResult.messageId}`);
            } else {
                console.error('❌ Failed to send problematic stocks report');
                console.error(`Error: ${emailResult.error?.message}`);
            }

        } catch (error) {
            console.error('💥 Error in checkAndSendProblematicStocksReport:', error);
            throw error;
        }
    }

    /**
     * Identify specific issues with a stock entry
     */
    private identifyStockIssues(stock: IRawMaterialStockDetails): string[] {
        const issues: string[] = [];

        if (stock.totalStock < 0) {
            issues.push('Negative Total Stock');
        }

        if (stock.usableStock < 0) {
            issues.push('Negative Usable Stock');
        }

        if (stock.usableStock > stock.totalStock) {
            issues.push('Usable > Total Stock');
        }

        return issues;
    }


    /**
     * Stop the cron job (if needed)
     */
    public stopCronJob(): void {
        // This would require storing the cron job reference
        // For now, just log that it should be stopped
        console.log('🛑 Cron job stop requested');
    }
} 