require('dotenv').config();
import { Sequelize } from 'sequelize';


enum APP_ENV {
    dev = "dev",
    test = "test",
    prod = "prod",
}

let WorkingENV: any = APP_ENV.prod;

const neonDBConnectionString = process.env.NEON_DB_CONNECTION_STRING || "";
// const neonDBStaging = process.env.NEON_DB_STAGING || "";
// const neonDBProductionStaging = process.env.NEON_DB_STAGING_MAIN || "";

let username = "";
let password = "";
let database = "";
let host = "";

if (WorkingENV === APP_ENV.dev) {
    username = process.env.DEV_DB_USERNAME || "";
    password = process.env.DEV_DB_PASSWORD || "";
    database = process.env.DEV_DB_NAME || "";
    host = process.env.DEV_DB_HOST || "";
}
else if (WorkingENV === APP_ENV.prod) {
    username = process.env.PROD_DB_USERNAME || "";
    password = process.env.PROD_DB_PASSWORD || "";
    database = process.env.PROD_DB_NAME || "";
    host = process.env.PROD_DB_HOST || "";
}


let sequelizeInit: Sequelize;

if (WorkingENV === APP_ENV.dev) {

    sequelizeInit = new Sequelize(
        database, username, password
        , {
            dialect: "postgres",
            host: host,
            logging: false
        });
}
else if (WorkingENV === APP_ENV.test) {
    // sequelizeInit = new Sequelize(neonDBStaging, {
        // logging: false,
    // });

}

else if (WorkingENV === APP_ENV.prod) {

    sequelizeInit = new Sequelize(neonDBConnectionString, {
        logging: false
    });

}

export { sequelizeInit, WorkingENV, APP_ENV };